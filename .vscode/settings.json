{"[json]": {"editor.defaultFormatter": "biomejs.biome"}, "[jsonc]": {"editor.defaultFormatter": "biomejs.biome"}, "biome.enabled": true, "editor.codeActionsOnSave": {"source.fixAll.biome": "explicit"}, "editor.defaultFormatter": "biomejs.biome", "editor.quickSuggestions": {"strings": "on"}, "eslint.enable": false, "files.readonlyInclude": {"**/routeTree.gen.ts": true}, "files.watcherExclude": {"**/routeTree.gen.ts": true}, "javascript.preferences.importModuleSpecifier": "shortest", "prettier.enable": false, "search.exclude": {"**/routeTree.gen.ts": true}, "typescript.disableAutomaticTypeAcquisition": false, "typescript.preferences.autoImportSpecifierExcludeRegexes": ["^(node:)?os$"], "typescript.preferences.importModuleSpecifier": "project-relative", "typescript.tsdk": "node_modules/typescript/lib", "tailwindCSS.classAttributes": ["class", "className", "ngClass"], "tailwindCSS.classFunctions": ["clsx"]}