
# Mass
## Setup
Guide for how to install the required tools.
You dont need to install anything else.

> I suggest using git bash for windows users

- clone the repo
- install [prototools](https://moonrepo.dev/docs/proto/install)
- run `proto use` in the root directory and restart the terminal session
- run `moon sync projects` in the root directory

# Usage (Post Setup)

- `bun run commit`: commit with commitlint
-  `moon run dahsboard:dev`: development
-  `moon run dahsboard:build`: building the project
-  `moon run dahsboard:start`: starting the project
-  `moon run :check`: linting and formatting with errors
-  `moon run :apply`: linting and formatting
-  `moon run :apply-unsafe`: linting and formatting with unsafe rules