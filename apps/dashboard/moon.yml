language: "typescript"
type: "library"
platform: "bun"

tasks:
  dev:
    local: true
    command: "vite --host"
    deps: ["icons:dev", "components:dev", "api:dev", "utils:dev"]
    env:
      NODE_ENV: development
    inputs:
      - "src/**/*"
    outputs:
      - "dist/**"
  build:
    command: "bun run tsc -b && bun run vite build && bun exec 'rm -rf dist/package.json' && bun exec 'rm -rf dist/tsconfig.json'"
    deps: ["icons:build", "components:build", "api:build", "utils:build"]
    inputs:
      - "src/**/*"
    outputs:
      - "dist/**"
  start:
    local: true
    deps: ["dashboard:build"]
    command: "bunx serve dist -s"
    inputs:
      - "dist/**"
