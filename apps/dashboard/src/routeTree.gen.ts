/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as CommonRouteImport } from './routes/_common'
import { Route as AnonRouteImport } from './routes/_anon'
import { Route as CommonSettingsRouteImport } from './routes/_common/settings'
import { Route as CommonNotificationsRouteImport } from './routes/_common/notifications'
import { Route as CommonFaqRouteImport } from './routes/_common/faq'
import { Route as CommonComplaintsRequestsRouteImport } from './routes/_common/complaints-requests'
import { Route as CommonSubscriptionsRouteImport } from './routes/_common/_subscriptions'
import { Route as AnonLoginRouteImport } from './routes/_anon/login'
import { Route as CommonSubscriptionsIndexRouteImport } from './routes/_common/_subscriptions/index'

const CommonRoute = CommonRouteImport.update({
  id: '/_common',
  getParentRoute: () => rootRouteImport,
} as any)
const AnonRoute = AnonRouteImport.update({
  id: '/_anon',
  getParentRoute: () => rootRouteImport,
} as any)
const CommonSettingsRoute = CommonSettingsRouteImport.update({
  id: '/settings',
  path: '/settings',
  getParentRoute: () => CommonRoute,
} as any)
const CommonNotificationsRoute = CommonNotificationsRouteImport.update({
  id: '/notifications',
  path: '/notifications',
  getParentRoute: () => CommonRoute,
} as any)
const CommonFaqRoute = CommonFaqRouteImport.update({
  id: '/faq',
  path: '/faq',
  getParentRoute: () => CommonRoute,
} as any)
const CommonComplaintsRequestsRoute =
  CommonComplaintsRequestsRouteImport.update({
    id: '/complaints-requests',
    path: '/complaints-requests',
    getParentRoute: () => CommonRoute,
  } as any)
const CommonSubscriptionsRoute = CommonSubscriptionsRouteImport.update({
  id: '/_subscriptions',
  getParentRoute: () => CommonRoute,
} as any)
const AnonLoginRoute = AnonLoginRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => AnonRoute,
} as any)
const CommonSubscriptionsIndexRoute =
  CommonSubscriptionsIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => CommonSubscriptionsRoute,
  } as any)

export interface FileRoutesByFullPath {
  '/login': typeof AnonLoginRoute
  '/complaints-requests': typeof CommonComplaintsRequestsRoute
  '/faq': typeof CommonFaqRoute
  '/notifications': typeof CommonNotificationsRoute
  '/settings': typeof CommonSettingsRoute
  '/': typeof CommonSubscriptionsIndexRoute
}
export interface FileRoutesByTo {
  '/login': typeof AnonLoginRoute
  '/complaints-requests': typeof CommonComplaintsRequestsRoute
  '/faq': typeof CommonFaqRoute
  '/notifications': typeof CommonNotificationsRoute
  '/settings': typeof CommonSettingsRoute
  '/': typeof CommonSubscriptionsIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/_anon': typeof AnonRouteWithChildren
  '/_common': typeof CommonRouteWithChildren
  '/_anon/login': typeof AnonLoginRoute
  '/_common/_subscriptions': typeof CommonSubscriptionsRouteWithChildren
  '/_common/complaints-requests': typeof CommonComplaintsRequestsRoute
  '/_common/faq': typeof CommonFaqRoute
  '/_common/notifications': typeof CommonNotificationsRoute
  '/_common/settings': typeof CommonSettingsRoute
  '/_common/_subscriptions/': typeof CommonSubscriptionsIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/login'
    | '/complaints-requests'
    | '/faq'
    | '/notifications'
    | '/settings'
    | '/'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/login'
    | '/complaints-requests'
    | '/faq'
    | '/notifications'
    | '/settings'
    | '/'
  id:
    | '__root__'
    | '/_anon'
    | '/_common'
    | '/_anon/login'
    | '/_common/_subscriptions'
    | '/_common/complaints-requests'
    | '/_common/faq'
    | '/_common/notifications'
    | '/_common/settings'
    | '/_common/_subscriptions/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  AnonRoute: typeof AnonRouteWithChildren
  CommonRoute: typeof CommonRouteWithChildren
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_common': {
      id: '/_common'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof CommonRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_anon': {
      id: '/_anon'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AnonRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_common/settings': {
      id: '/_common/settings'
      path: '/settings'
      fullPath: '/settings'
      preLoaderRoute: typeof CommonSettingsRouteImport
      parentRoute: typeof CommonRoute
    }
    '/_common/notifications': {
      id: '/_common/notifications'
      path: '/notifications'
      fullPath: '/notifications'
      preLoaderRoute: typeof CommonNotificationsRouteImport
      parentRoute: typeof CommonRoute
    }
    '/_common/faq': {
      id: '/_common/faq'
      path: '/faq'
      fullPath: '/faq'
      preLoaderRoute: typeof CommonFaqRouteImport
      parentRoute: typeof CommonRoute
    }
    '/_common/complaints-requests': {
      id: '/_common/complaints-requests'
      path: '/complaints-requests'
      fullPath: '/complaints-requests'
      preLoaderRoute: typeof CommonComplaintsRequestsRouteImport
      parentRoute: typeof CommonRoute
    }
    '/_common/_subscriptions': {
      id: '/_common/_subscriptions'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof CommonSubscriptionsRouteImport
      parentRoute: typeof CommonRoute
    }
    '/_anon/login': {
      id: '/_anon/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof AnonLoginRouteImport
      parentRoute: typeof AnonRoute
    }
    '/_common/_subscriptions/': {
      id: '/_common/_subscriptions/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof CommonSubscriptionsIndexRouteImport
      parentRoute: typeof CommonSubscriptionsRoute
    }
  }
}

interface AnonRouteChildren {
  AnonLoginRoute: typeof AnonLoginRoute
}

const AnonRouteChildren: AnonRouteChildren = {
  AnonLoginRoute: AnonLoginRoute,
}

const AnonRouteWithChildren = AnonRoute._addFileChildren(AnonRouteChildren)

interface CommonSubscriptionsRouteChildren {
  CommonSubscriptionsIndexRoute: typeof CommonSubscriptionsIndexRoute
}

const CommonSubscriptionsRouteChildren: CommonSubscriptionsRouteChildren = {
  CommonSubscriptionsIndexRoute: CommonSubscriptionsIndexRoute,
}

const CommonSubscriptionsRouteWithChildren =
  CommonSubscriptionsRoute._addFileChildren(CommonSubscriptionsRouteChildren)

interface CommonRouteChildren {
  CommonSubscriptionsRoute: typeof CommonSubscriptionsRouteWithChildren
  CommonComplaintsRequestsRoute: typeof CommonComplaintsRequestsRoute
  CommonFaqRoute: typeof CommonFaqRoute
  CommonNotificationsRoute: typeof CommonNotificationsRoute
  CommonSettingsRoute: typeof CommonSettingsRoute
}

const CommonRouteChildren: CommonRouteChildren = {
  CommonSubscriptionsRoute: CommonSubscriptionsRouteWithChildren,
  CommonComplaintsRequestsRoute: CommonComplaintsRequestsRoute,
  CommonFaqRoute: CommonFaqRoute,
  CommonNotificationsRoute: CommonNotificationsRoute,
  CommonSettingsRoute: CommonSettingsRoute,
}

const CommonRouteWithChildren =
  CommonRoute._addFileChildren(CommonRouteChildren)

const rootRouteChildren: RootRouteChildren = {
  AnonRoute: AnonRouteWithChildren,
  CommonRoute: CommonRouteWithChildren,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
