import { authApi, global$, settingApi } from '@mass/api'
import { detector } from '@mass/utils'
import { createRootRouteWithContext, Outlet } from '@tanstack/react-router'
import { TanStackRouterDevtools } from '@tanstack/react-router-devtools'
import clsx from 'clsx'
import { Toaster } from 'react-hot-toast'

export const Route = createRootRouteWithContext()({
  component: () => (
    <>
      <Outlet />
      <Toaster
        position='top-right'
        toastOptions={{
          className: clsx('border border-accessory-1 rounded-max!', 'text-2xs'),
        }}
      />
      <TanStackRouterDevtools position='bottom-left' />
    </>
  ),

  async beforeLoad() {
    try {
      if (!global$.documents.pdfs.get()) {
        const documents = await settingApi.documents.pdf({
          invalidateCache: true,
        })

        global$.documents.pdfs.set({
          agreement: documents.value.agreement.url,
          kvkk: documents.value.kvkk.url,
          about: documents.value.about.url,
          userGuide: documents.value.manual.url,
        })
      }

      // If user is already logged in, return
      if (global$.isUserValid()) {
        return
      }

      // Check if user is logged in (cookie)
      global$.user.set(
        await authApi.me({
          invalidateCache: true,
        }),
      )

      // If user is not logged in, return
      if (!global$.isUserValid()) {
        return
      }

      // Get user settings
      const [pdpl, userAgreement, language] = await Promise.all([
        settingApi.user({
          query: {
            key: 'agreements.kvkk',
          },
        }),
        settingApi.user({
          query: {
            key: 'agreements.user-agreement',
          },
        }),
        settingApi.user({
          query: {
            key: 'lang',
          },
        }),
      ])

      global$.aggreements.set({
        pdpl: pdpl.value === 'true',
        userAgreement: userAgreement.value === 'true',
      })

      const rawDetectedLanguage = detector.detect()
      const detectedLanguage = (
        (typeof rawDetectedLanguage === 'string' ? rawDetectedLanguage : rawDetectedLanguage?.at(0)) ?? ''
      ).split('-')[0]
      const targetLanguage = (language.value ?? detectedLanguage ?? 'tr') as 'tr' | 'en'

      await settingApi.changeLangauge(targetLanguage, language.value)

      // biome-ignore lint/suspicious/noEmptyBlockStatements: Redundant
    } catch {}
  },
})
