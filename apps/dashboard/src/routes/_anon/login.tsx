import 'slick-carousel/slick/slick.css'

import { authApi } from '@mass/api'
import { EGovernmentButton } from '@mass/components/dashboard'
import { Link, Text, Title } from '@mass/components/shared'
import { createFileRoute } from '@tanstack/react-router'
import { useCallback } from 'react'
import { Trans, useTranslation } from 'react-i18next'

function Index() {
  const { t: common } = useTranslation('common')
  const { t: dashboard } = useTranslation('dashboard')

  const handleLogin = useCallback(() => {
    authApi.login.end()
  }, [])

  const handleOpenFile = useCallback((type: 'pdpl' | 'user-agreement') => {
    console.log('handleOpenFile', type)
  }, [])

  return (
    <div className='flex flex-col gap-4 max-xl:items-center'>
      <Title>{dashboard('auth.login')}</Title>
      <Text variant='subtitle'>{dashboard('auth.login-description')}</Text>

      <EGovernmentButton onClick={handleLogin} className='my-10'>
        {dashboard('auth.login-with-e-government')}
      </EGovernmentButton>

      <Text variant='dimmer' className='max-xl:text-center'>
        <Trans
          ns='dashboard'
          i18nKey='auth.before-login'
          components={{
            pdplLink: <Link onClick={() => handleOpenFile('pdpl')} />,
            userAgreementLink: <Link onClick={() => handleOpenFile('user-agreement')} />,
          }}
          values={{
            pdplText: common('pdpl-long'),
            userAgreementText: common('user-agreement'),
          }}
        />
      </Text>
    </div>
  )
}

export const Route = createFileRoute('/_anon/login')({
  component: Index,
})
