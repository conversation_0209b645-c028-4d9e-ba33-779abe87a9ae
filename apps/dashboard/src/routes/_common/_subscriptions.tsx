import { global$, subscriptionsApi } from '@mass/api'
import { SubscriptionFiltersModal, SubscriptionLayout } from '@mass/components/dashboard'
import { createFileRoute, Outlet } from '@tanstack/react-router'

function RouteComponent() {
  return (
    <SubscriptionLayout>
      <SubscriptionFiltersModal />
      <Outlet />
    </SubscriptionLayout>
  )
}

export const Route = createFileRoute('/_common/_subscriptions')({
  async beforeLoad() {
    const regions = await subscriptionsApi.regions()
    const subscriptions = await subscriptionsApi.subscriptions({
      invalidateCache: true,
    })

    global$.subscription.regions.set(regions)
    global$.subscription.subscriptions.set(subscriptions)
  },

  component: RouteComponent,
})
