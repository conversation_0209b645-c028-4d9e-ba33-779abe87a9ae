import { use$ } from '@legendapp/state/react'
import { global$ } from '@mass/api'
import { Badge, Button, Link, Title } from '@mass/components/shared'
import { BuildingIcon, DotsVerticalIcon } from '@mass/icons'
import { createFileRoute } from '@tanstack/react-router'
import clsx from 'clsx'
import { useTranslation } from 'react-i18next'

function Home() {
  const { t: common } = useTranslation('common')
  const subscriptions = use$(() => global$.subscription.subscriptions.get()?.content ?? [])

  const getRegionName = (regionId: string) => {
    const targetRegion = (global$.subscription.regions.get() ?? []).find(region => region.id === regionId)
    return targetRegion?.name ?? ''
  }

  return (
    <div className='grid gap-8 p-16 md:grid-cols-2 xl:grid-cols-3'>
      {subscriptions.map(subscription => (
        <div className={clsx('relative')} key={subscription.id}>
          <Link
            to={`/${subscription.id}`}
            className={clsx(
              'flex flex-col items-start gap-0!', // flex
              'w-full', // sizing
              'rounded-b2 border border-accessory-1', // border
              'hover:border-dim-3',
            )}>
            <div
              className={clsx(
                'w-full', // sizing
                'flex flex-row items-center', // flex
                'gap-8 p-10', // spacing
              )}>
              <div
                className={clsx(
                  'flex flex-col items-center justify-center', // flex
                  'h-24 w-24', // sizing
                  'rounded-b2 border border-accessory-1', // border
                  'shadow-layer-2',
                )}>
                <BuildingIcon className='h-12 w-12 text-dim-1' />
              </div>

              <Title variant='h5' el='h2'>
                {subscription.name}
              </Title>
            </div>

            <div
              className={clsx(
                'w-full', // sizing
                'flex flex-row items-center', // flex
                'gap-8 px-10 pb-10', // spacing
              )}>
              <Badge withDot> {getRegionName(subscription.regionId)} </Badge>

              {subscription.type === 'electricity-production' && <Badge mode='success'>{common('production')}</Badge>}
            </div>
          </Link>
          <Button variant='icon' className='absolute top-10 right-10'>
            <DotsVerticalIcon className='text-dim-3' />
          </Button>
        </div>
      ))}
    </div>
  )
}

export const Route = createFileRoute('/_common/_subscriptions/')({
  component: Home,
})
