{"devDependencies": {"@biomejs/biome": "2.0.6", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@commitlint/prompt-cli": "^19.8.1", "@types/bun": "^1.2.17", "@types/react": "^19.1.8", "husky": "^9.1.7", "typescript": "^5.8.3"}, "name": "mass", "private": "true", "scripts": {"commit": "commit", "prepare": "husky || true"}, "type": "module", "version": "0.0.0", "workspaces": ["{packages,plugins,apps,tools,experiments}/**"]}