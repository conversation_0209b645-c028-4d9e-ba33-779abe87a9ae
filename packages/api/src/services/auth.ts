import { type } from 'arktype'
import { global$ } from '../stores/global'
import { apiBuilder } from '../utils/api-builder'

const BASE_USER_TYPE = type({
  id: 'string',
  permissions: type({
    scope: 'string',
  }).array(),
})

export const authApi = {
  me: apiBuilder({
    url: '/auth/me',
    cache: 'validate',

    // data
    response: type(
      BASE_USER_TYPE.merge({
        type: "'anon'",
      }),
    ).or(
      BASE_USER_TYPE.merge({
        type: "'end'",
        email: 'string | null',
        phone: 'string | null',
        tckn: 'string',
        firstName: 'string',
        lastName: 'string',
      }),
    ),
  }),

  login: {
    end: () => {
      const url = '/api/auth/edevlet'
      window.open(url, '_self')
    },
  },

  logout: apiBuilder({
    method: 'DELETE',
    url: '/auth/logout',

    response: type('string'),

    cb: () => {
      global$.user.set(null)
    },
  }),
} as const
