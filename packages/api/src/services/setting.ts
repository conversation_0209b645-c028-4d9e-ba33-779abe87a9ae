import { i18n } from '@mass/utils'
import { type } from 'arktype'

import { global$ } from '../stores/global'
import { apiBuilder } from '../utils/api-builder'

export const settingApi = {
  user: apiBuilder({
    url: '/setting/user/$key',

    // data
    query: type({
      key: 'string',
    }),

    response: type({
      value: 'string',
    }),
  }),

  updateUser: apiBuilder({
    method: 'PATCH',
    url: '/setting/user/$key',

    // data
    query: type({
      key: "'lang' | 'agreements.user-agreement' | 'agreements.kvkk'",
    }),
    payload: type({
      value: 'string',
    }),
    response: type('unknown'),
  }),

  changeLangauge: async (lang: 'tr' | 'en', currentLang: string | null) => {
    if (global$.isUserValid() && currentLang !== lang) {
      await settingApi.updateUser({
        query: {
          key: 'lang',
        },
        payload: {
          value: lang,
        },
      })
    }

    global$.language.set(lang)
    i18n.changeLanguage(lang)
    document.querySelector('html')?.setAttribute('lang', lang)
  },

  documents: {
    pdf: apiBuilder({
      cache: 'until-reload',
      url: '/setting/global/documents.pdf',

      response: type({
        value: {
          "['agreement' | 'kvkk' | 'about' | 'manual']": {
            url: 'string',
          },
        },
      }),
    }),
  },
} as const
