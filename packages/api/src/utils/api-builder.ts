/** biome-ignore-all lint/nursery/noExcessiveLinesPerFunction: Redundant */
import { type BlobType, generateCacheKey, isPromise, type Merge } from '@mass/utils'
import { type } from 'arktype'

const normalizeObjectParams = (data: BlobType) => {
  return Object.entries(data)
    .map(e => e.join('='))
    .join('&')
}

const normalizeParams = (params: BlobType, keys: string[]) => {
  const normalized = new URLSearchParams()

  for (const [key, value] of Object.entries(params)) {
    if (keys.includes(key)) {
      if (Array.isArray(value)) {
        for (const item of value) {
          normalized.append(key, typeof item === 'object' && item !== null ? normalizeObjectParams(item) : item)
        }
      } else if (typeof value === 'object' && value !== null) {
        normalized.set(key, normalizeObjectParams(value))
      } else {
        normalized.set(key, value as BlobType)
      }

      continue
    }

    normalized.set(key, value as BlobType)
  }

  return normalized
}

export const apiBuilder = <const D extends Api.Definition>(
  definition: D,
  defaultPayload: Api.GetPayload<D> = {} as Api.GetPayload<D>,
) => {
  const targetApi = Object.assign(
    {
      method: 'GET',
      url: '/test',
      cache: 'none',

      // data
      query: type({}),
      params: type({}),
      payload: type({}),
      response: type({}),

      encodeParamsKeysUrl: [],
    },
    definition,
  ) as unknown as Merge<Required<Api.Definition>, Required<D>>

  const caches = new Map<string, BlobType>()

  return async (
    options: {
      payload?: Api.GetPayload<D>
      query?: Api.GetQuery<D>
      params?: Api.GetParams<D>
      invalidateCache?: boolean
    } = {},
    // biome-ignore lint/complexity/noExcessiveCognitiveComplexity: Redundant
  ): Promise<Api.GetResponse<D>> => {
    const { promise, resolve, reject } = Promise.withResolvers()

    const isCached = targetApi.cache === 'until-reload' || (targetApi.cache === 'validate' && !options.invalidateCache)

    options.params = (options.params ?? {}) as BlobType
    options.query = (options.query ?? {}) as BlobType
    options.payload = (options.payload ?? {}) as BlobType

    const hashKey = isCached ? generateCacheKey(options) : ''

    if (isCached) {
      const cached = caches.get(hashKey)

      if (isPromise(cached)) {
        return await cached
      }
      if (cached) {
        return cached as Api.GetResponse<D>
      }

      caches.set(hashKey, promise)
    }

    const payload = Object.assign({}, defaultPayload, options.payload)
    const payloadOut = targetApi.payload(payload)
    const queryOut = targetApi.query(options.query)
    const paramsOut = targetApi.params(options.params)

    if (payloadOut instanceof type.errors) {
      console.error(`Api: ${name} - Invalid payload`, payloadOut)
    } else if (queryOut instanceof type.errors) {
      console.error(`Api: ${name} - Invalid query`, queryOut)
    } else if (paramsOut instanceof type.errors) {
      console.error(`Api: ${name} - Invalid params`, paramsOut)
    }

    const targetParams = normalizeParams(options.params, targetApi.encodeParamsKeysUrl ?? [])

    try {
      let url = `/api${targetApi.url}`

      const queryEntries = Object.entries(options.query ?? {})

      if (queryEntries.length > 0) {
        for (const [key, value] of queryEntries) {
          url = url.replace(`$${key}`, value as BlobType)
        }
      }

      const requestOptions = {
        method: targetApi.method,
        credentials: 'include',
      } as RequestInit

      if ((targetApi.method as string) !== 'GET') {
        requestOptions.body = JSON.stringify(payload)
      }

      if (targetParams.size > 0) {
        url += '?'

        for (const [key, value] of targetParams.entries()) {
          url += `${key}=${value}&`
        }
      }

      const response = await fetch(url, requestOptions)

      if (!response.ok) {
        throw new Error(`Api: ${name} - ${response.status} ${response.statusText}`)
      }

      const responseData = await response.json()

      if (isCached) {
        resolve(responseData)

        caches.set(hashKey, responseData)
      }

      if (definition.cb) {
        const result = await definition.cb({
          ...options,
          response: responseData,
        })

        if (result) {
          return result as Api.GetResponse<D>
        }
      }

      return responseData as Api.GetResponse<D>
    } catch (err) {
      console.error(`Api: ${name}`, { name, payload, targetApi })
      console.error(err)

      if (isCached) {
        caches.delete(hashKey)
        reject(err)
      }

      throw err
    }
  }
}
