declare global {
  namespace Dashboard {
    type Modals =
      | 'subscription-filters'
      | 'add-new-subscription'
      | `document-${keyof Api.Stores.Global['aggreements']}`
      | 'test'

    type SubscriptionParams = Api.ExtractParams<Api.Services['subscriptions']['subscriptions']>

    interface SubscriptionFilters {
      params: SubscriptionParams

      addFilterEq: <T extends keyof SubscriptionParams['filter:eq']>(
        type: T,
        value: SubscriptionParams['filter:eq'][T],
      ) => void

      removeFilterEq: (type: keyof SubscriptionParams['filter:eq']) => void

      clearFilters: () => void
    }
  }
}
