import { useLocation } from '@tanstack/react-router'
import { useMemo } from 'react'
import { useTranslation } from 'react-i18next'

const routeEntries = [[/^\/$/, 'subscriptions.title', 'subscriptions.description', false]] as const

export const useMeta = () => {
  const { pathname } = useLocation()
  const { t: dashboard } = useTranslation('dashboard')

  return useMemo(() => {
    const foundRoute = routeEntries.find(([regex]) => regex.test(pathname))

    if (!foundRoute) {
      return {
        title: null,
        description: null,
        hasTabs: false,
      }
    }

    return {
      title: dashboard(foundRoute[1]),
      description: dashboard(foundRoute[2]),
      hasTabs: foundRoute[3],
    }
  }, [pathname, dashboard])
}
