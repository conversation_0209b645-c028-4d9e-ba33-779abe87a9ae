import 'slick-carousel/slick/slick.css'

import { AppleStore, GoogleStore } from '@mass/icons'
import clsx from 'clsx'
import type { FC } from 'react'
import { Trans, useTranslation } from 'react-i18next'
import Slider from 'react-slick'
import { Text } from '../../../shared'

export const AuthAside: FC<{
  className?: string
}> = ({ className }) => {
  const { t: dashboard } = useTranslation('dashboard')

  return (
    <aside
      className={clsx(
        'relative flex overflow-hidden', // other
        'h-full w-full max-md:h-[450px] md:max-xl:h-[550px]', // sizing
        'rounded-c1', // styling
        className,
      )}>
      <div className='relative z-10 h-full w-full'>
        <Slider
          infinite
          fade
          dots
          arrows={false}
          slidesToShow={1}
          slidesToScroll={1}
          className='h-full'
          customPaging={() => <button type='button' />}
          autoplay
          autoplaySpeed={8000}>
          {/* Slide 1 */}

          <div className='relative h-full w-full p-10'>
            <img
              src='/dashboard/auth/bg-orange.png'
              alt='slide-1'
              className={clsx(
                'absolute inset-0', // positioning
                'h-full w-full', // sizing
                'pointer-events-none object-cover',
              )}
            />
            <div className='relative z-10 h-full w-full'>
              <img
                src='/dashboard/auth/hand.png'
                alt='hand'
                className={clsx(
                  '-bottom-10 absolute', // positioning
                  'h-full w-full', // sizing
                  'pointer-events-none object-contain object-bottom',
                )}
              />

              <img
                src='/dashboard/auth/orange-card-1.png'
                alt='hand'
                className={clsx(
                  '-translate-y-3/4 -translate-x-1/6 absolute top-1/2 left-1/6', // positioning
                  'aspect-video max-w-[150px] md:max-w-[20vw]', // sizing
                  'pointer-events-none object-contain object-center',
                )}
              />

              <img
                src='/dashboard/auth/orange-card-2.png'
                alt='hand'
                className={clsx(
                  '-translate-y-3/4 max-xl:-bottom-20 absolute right-1/6 bottom-1/2 translate-x-1/6 max-sm:right-10', // positioning
                  'aspect-video max-w-[150px] md:max-w-[20vw]', // sizing
                  'pointer-events-none object-contain object-center',
                )}
              />

              <Text
                variant='slide'
                className='-translate-x-1/2 absolute top-10 left-1/2 z-10 flex flex-col items-center text-nowrap md:top-20'>
                <Trans
                  i18nKey='auth.slide-1'
                  ns='dashboard'
                  components={{ bold: <b className='block font-semibold' /> }}
                />
              </Text>
            </div>

            <div
              className={clsx(
                'absolute bottom-20 left-10 z-10 sm:left-20', // positioning
                'flex flex-col items-start gap-3 max-sm:items-center', // flex
              )}>
              {/** biome-ignore lint/a11y/useValidAnchor: Redundant */}
              <a href='#' className='cursor-pointer'>
                <AppleStore className='max-md:h-[43px] max-md:w-[128px]' />
              </a>
              {/** biome-ignore lint/a11y/useValidAnchor: Redundant */}
              <a href='#' className='cursor-pointer'>
                <GoogleStore className='max-md:h-[38px] max-md:w-[127px]' />
              </a>
            </div>
          </div>

          {/* Slide 2 */}

          <div className='relative h-full w-full p-10'>
            <img
              src='/dashboard/auth/bg-blue.png'
              alt='slide-2'
              className={clsx(
                'absolute inset-0', // positioning
                'h-full w-full', // sizing
                'pointer-events-none object-cover',
              )}
            />
            <div className='relative z-10 h-full w-full'>
              <img
                src='/dashboard/auth/blue-card-1.png'
                alt='hand'
                className={clsx(
                  '-bottom-10 -right-10 absolute', // positioning
                  'h-3/4', // sizing
                  'pointer-events-none object-cover object-left',
                )}
              />

              <Text
                variant='slide'
                className='-translate-x-1/2 absolute top-10 left-1/2 z-10 w-full text-center md:top-20'>
                {dashboard('auth.slide-2')}
              </Text>
            </div>
          </div>

          {/* Slide 3 */}

          <div className='relative h-full w-full p-10'>
            <img
              src='/dashboard/auth/bg-purple.png'
              alt='slide-3'
              className={clsx(
                'absolute inset-0', // positioning
                'h-full w-full', // sizing
                'pointer-events-none object-cover',
              )}
            />
            <div className='relative z-10 h-full w-full'>
              <img
                src='/dashboard/auth/purple-card-1.png'
                alt='hand'
                className={clsx(
                  '-bottom-10 -right-10 absolute', // positioning
                  'h-3/4', // sizing
                  'pointer-events-none object-cover object-left',
                )}
              />

              <Text
                variant='slide'
                className='-translate-x-1/2 absolute top-10 left-1/2 z-10 w-full text-center md:top-20'>
                {dashboard('auth.slide-3')}
              </Text>
            </div>
          </div>
        </Slider>
      </div>
    </aside>
  )
}
