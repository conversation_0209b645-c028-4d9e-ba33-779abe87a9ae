import { Copyright } from '@mass/icons'
import clsx from 'clsx'
import type { FC } from 'react'
import { useTranslation } from 'react-i18next'
import { Text } from '../../../shared'

export const AuthFooter: FC = () => {
  const { t: common } = useTranslation('common')
  const { t: dashboard } = useTranslation('dashboard')

  return (
    <footer
      className={clsx(
        'flex items-center gap-3', // flex
        'xl:mx-auto', // centering
      )}>
      <div className='flex items-center gap-2'>
        <Text variant='dim' className='uppercase'>
          {common('project')}
        </Text>
        <Copyright className='h-6 w-6' />
        <Text variant='dim'>{dashboard('all-rights-reserved')}</Text>
      </div>

      <span className='h-1.5 w-1.5 rounded-full bg-dim-1' />

      <Text variant='dim'>
        <a href='/about'>{dashboard('about-app')}</a>
      </Text>
    </footer>
  )
}
