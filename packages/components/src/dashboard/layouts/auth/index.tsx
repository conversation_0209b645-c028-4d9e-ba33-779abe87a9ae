import clsx from 'clsx'
import type { FC } from 'react'
import { AuthAside } from './aside'
import { AuthFooter } from './footer'
import { AuthHeader } from './header'

export const AuthLayout: FC<{
  children: React.ReactNode
}> = ({ children }) => {
  return (
    <div
      className={clsx(
        'flex flex-col gap-16 max-xl:items-center xl:flex-row xl:gap-50', // flex
        'max-xl:max-w-[500px] md:max-xl:max-w-[800px] xl:h-screen xl:max-h-screen xl:w-screen', // sizing
        'py-21 pr-21 pl-50 max-sm:px-12 max-xl:mx-auto max-xl:px-21 max-xs:px-8 max-xs:py-15', // padding and centering
      )}>
      <AuthHeader className='xl:hidden' />
      {/* Aside */}
      <AuthAside className='order-0 xl:order-1' />

      {/* Auth page */}
      <main
        className={clsx(
          'flex flex-col justify-between max-xl:items-center max-xl:gap-30', // flex
          'w-full max-w-[500px] xl:my-auto xl:h-full xl:max-h-4/5 xl:max-w-[400px]', // sizing
        )}>
        <AuthHeader className='max-xl:hidden' />
        {children}
        <AuthFooter />
      </main>
    </div>
  )
}
