import clsx from 'clsx'
import type { FC } from 'react'

import { Navigation } from './navigation'

export const CommonLayout: FC<{
  children: React.ReactNode
}> = ({ children }) => {
  return (
    <div className={clsx('flex')}>
      {/* Nav */}
      <Navigation />

      {/* Common pages */}
      <main
        className={clsx(
          'flex flex-col', // flex
          'h-auto min-h-screen w-full', // sizing
        )}>
        {children}
      </main>
    </div>
  )
}
