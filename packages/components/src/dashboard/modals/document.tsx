// biome-ignore-all lint/nursery/noExcessiveLinesPerFunction: Redundant

import { Memo, useObservable, useObserve } from '@legendapp/state/react'
import { global$, settingApi } from '@mass/api'
import { useScroll } from '@mass/utils'
import type { PDFDocumentProxy } from 'pdfjs-dist'
import { type FC, memo, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { Document, Page, pdfjs } from 'react-pdf'

pdfjs.GlobalWorkerOptions.workerSrc = new URL('pdfjs-dist/build/pdf.worker.min.mjs', import.meta.url).toString()

import {
  Button,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogOverlay,
  DialogPortal,
  DialogRoot,
  Spinner,
  ui$,
} from '../../shared'

const options = {
  cMapUrl: '/cmaps/',
  standardFontDataUrl: '/standard_fonts/',
  wasmUrl: '/wasm/',
}

export const DocumentModal: FC<{
  label: string
  suffix: string
  storeKey: keyof Api.Stores.Global['aggreements']
  apiKey: Exclude<Api.ExtractQuery<(typeof settingApi)['updateUser']>['key'], 'lang'>
  resultKey: keyof Exclude<Api.Stores.Global['documents']['pdfs'], null>

  canBeClosed?: boolean
}> = memo(({ label, suffix, storeKey, apiKey, resultKey, canBeClosed = false }) => {
  const isRendered = useRef(false)
  const once = useRef(false)
  const { t: common } = useTranslation('common')

  const modalName = `dashboard.document-${storeKey}` as const
  const { t: dashboard } = useTranslation('dashboard')

  const localState = useObservable<{
    isAsked: boolean
    numPages: number
    completed: boolean
    pdfWidth: number | undefined
  }>({
    isAsked: false,
    numPages: -1,
    completed: false,
    pdfWidth: undefined,
  })

  const { ref, setRef, scroll } = useScroll(50, (node, throttledUpdate) => {
    if (once.current) {
      return
    }

    localState.pdfWidth.set(node.clientWidth - 50)

    const observer = new MutationObserver(mutationList => {
      if (isRendered.current) {
        throttledUpdate()
        observer.disconnect()

        return
      }
    })

    observer.observe(node, { attributes: true, childList: true, subtree: true })

    once.current = true
  })

  useObserve(() => {
    if (scroll.y.get() >= scroll.yMax.get() && scroll.dirty.get() && localState.numPages.get() >= 0) {
      localState.completed.set(true)
    }
  })

  useObserve(() => {
    const targetDocument = global$.aggreements[storeKey].get()
    const alreadyOpen = ui$.isModalOpen(modalName)

    if (!(targetDocument || alreadyOpen) && (canBeClosed ? !localState.isAsked.get() : true)) {
      ui$.onChangeModal(modalName, true)

      localState.isAsked.set(true)
    }
  })

  const handleAccept = async () => {
    if (!scroll.dirty.get()) {
      return
    }

    if (!localState.completed.get()) {
      ref.current?.scrollTo({
        top: scroll.yMax.get(),
        behavior: 'smooth',
      })

      return
    }

    await settingApi.updateUser({
      query: {
        key: apiKey,
      },

      payload: {
        value: 'true',
      },
    })
    global$.aggreements[storeKey].set(true)
    ui$.onChangeModal(modalName, false)
  }

  const onDocumentLoadSuccess = ({ numPages: nextNumPages }: PDFDocumentProxy): void => {
    localState.numPages.set(nextNumPages)
  }

  const pages = useObservable(() => {
    if (localState.pdfWidth.get() && localState.numPages.get() >= 0) {
      return Array.from(new Array(localState.numPages.get()), (_el, index) => (
        <Page
          key={`page_${index + 1}`}
          pageNumber={index + 1}
          // biome-ignore lint/style/noNonNullAssertion: Redundant
          width={localState.pdfWidth.get()!}
          loading={<Spinner />}
          noData={<Spinner />}
          onLoadSuccess={() => {
            isRendered.current = true
          }}
        />
      ))
    }

    return null
  })

  return (
    <DialogRoot name={modalName} noCloseButton sticky>
      <DialogPortal>
        <DialogOverlay />
        <DialogContent
          ref={setRef}
          className='max-w-[800px]'
          header={
            <DialogHeader
              title={label}
              description={dashboard('modals.documents.aggree-to', {
                fileName: label,
                suffix,
              })}
              disabled={!canBeClosed}
            />
          }
          footer={
            <DialogFooter>
              <Button variant='primary' className='mx-auto w-max' onClick={handleAccept}>
                <Memo>
                  {() => <>{localState.completed.get() ? common('accept') : dashboard('modals.documents.read-all')}</>}
                </Memo>
              </Button>
            </DialogFooter>
          }>
          <Memo>
            {() => (
              <Document
                options={options}
                loading={<Spinner />}
                noData={<Spinner />}
                // biome-ignore lint/style/noNonNullAssertion: Redundant
                {...(global$.documents.pdfs[resultKey].get() ? { file: global$.documents.pdfs[resultKey].get()! } : {})}
                onLoadSuccess={onDocumentLoadSuccess}>
                {pages.get()}
              </Document>
            )}
          </Memo>
        </DialogContent>
      </DialogPortal>
    </DialogRoot>
  )
})
