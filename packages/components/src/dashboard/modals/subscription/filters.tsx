import { FilterLinesIcon } from '@mass/icons'
import type { FC } from 'react'
import { toast } from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import {
  Button,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogOverlay,
  DialogPortal,
  DialogRoot,
  Select,
  Text,
  ui$,
} from '../../../shared'
import { subscriptionFilters$ } from '../../stores/subscription-filters'

export const SubscriptionFiltersModal: FC = () => {
  const { t: dashboard } = useTranslation('dashboard')
  const { t: common } = useTranslation('common')

  const handleClearFilters = () => {
    subscriptionFilters$.clearFilters()
    ui$.onChangeModal('dashboard.subscription-filters', false)

    toast.success(common('filters-cleared'))
  }

  const handleFilter = () => {
    ui$.onChangeModal('dashboard.subscription-filters', false)

    toast.success(common('filters-applied'))
  }

  return (
    <DialogRoot name='dashboard.subscription-filters'>
      <DialogPortal>
        <DialogOverlay />
        <DialogContent
          className='sm:max-w-[450px]'
          header={
            <DialogHeader
              icon={<FilterLinesIcon strokeWidth={2} className='h-12 w-12' />}
              title={dashboard('subscriptions.filters')}
            />
          }
          footer={
            <DialogFooter borderLess className='flex-row gap-4'>
              <Button variant='bordered' onClick={handleClearFilters}>
                {common('clear-filters')}
              </Button>
              <Button variant='primary' onClick={handleFilter}>
                {common('filter')}
              </Button>
            </DialogFooter>
          }>
          <div className='flex items-center justify-between gap-4'>
            <Text variant='subtitle'> {common('subscription-type')} </Text>

            <Select
              placeholder='Select a fruit...'
              options={[
                { value: 'apple', label: 'Apple' },
                { value: 'banana', label: 'Banana' },
                { value: 'grape', label: 'Grape', disabled: true },
              ]}
              onValueChange={value => console.log('Selected:', value)}
            />
          </div>
        </DialogContent>
      </DialogPortal>
    </DialogRoot>
  )
}
