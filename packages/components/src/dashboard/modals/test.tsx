import type { FC } from 'react'
import { DialogContent, DialogHeader, DialogOverlay, DialogPortal, DialogRoot } from '../../shared'

export const TestModal: FC = () => {
  return (
    <DialogRoot name='dashboard.test'>
      <DialogPortal>
        <DialogOverlay />
        <DialogContent className='sm:max-h-[200px] sm:max-w-[200px]'>
          <DialogHeader title='Test' description='lorem ipsum dolor' />
        </DialogContent>
      </DialogPortal>
    </DialogRoot>
  )
}
