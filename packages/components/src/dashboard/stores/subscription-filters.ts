import { observable } from '@legendapp/state'
import type { BlobType } from '@mass/utils'

export const subscriptionFilters$ = observable<Dashboard.SubscriptionFilters>({
  params: {},

  addFilterEq: (type, value) => {
    subscriptionFilters$.params.set(prev => ({
      ...prev,
      'filter:eq': [
        ...(prev['filter:eq'] ?? []),
        {
          [type]: value,
        },
      ],
    }))
  },

  removeFilterEq: type => {
    subscriptionFilters$.params.set(prev => {
      const filtered = (prev['filter:eq'] ?? []).filter(filter => filter[type])

      return {
        ...prev,
        ...(filtered.length > 0 ? { 'filter:eq': filtered } : { 'filter:eq': undefined }),
      } as BlobType
    })
  },

  clearFilters: () => {
    subscriptionFilters$.params.set({})
  },
})
