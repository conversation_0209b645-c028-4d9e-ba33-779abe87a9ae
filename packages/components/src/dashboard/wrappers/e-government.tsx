import { EGovernmentWhite } from '@mass/icons'
import clsx from 'clsx'
import type { FC } from 'react'
import { Button } from '../../shared'

export const EGovernmentButton: FC<
  React.ButtonHTMLAttributes<HTMLButtonElement> & {
    className?: string
    children: React.ReactNode
  }
> = ({ className, children, ...props }) => {
  return (
    <Button
      className={clsx(
        'bg-turkey font-semibold text-base text-white', // text
        'hover:bg-turkey/80', // hover
        className,
      )}
      {...props}>
      <EGovernmentWhite className='h-12 w-12' />
      {children}
    </Button>
  )
}
