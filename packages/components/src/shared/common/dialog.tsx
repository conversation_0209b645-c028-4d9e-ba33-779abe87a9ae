import { use$ } from '@legendapp/state/react'
import { XClose } from '@mass/icons'
import {
  Close,
  Content,
  Description,
  type DialogCloseProps,
  type DialogContentProps,
  type DialogOverlayProps,
  type DialogProps,
  Overlay,
  Portal,
  Root,
  Title,
} from '@radix-ui/react-dialog'
import clsx from 'clsx'
import { createContext, type FC, type ReactNode, use } from 'react'

import { ui$ } from '../stores/ui'
import { useButtonStyles } from './button'
import { useTextStyles } from './text'
import { useTitleStyles } from './title'

// biome-ignore lint/style/useComponentExportOnlyModules: Redundant
export const dialogContext = createContext({
  name: null as unknown as Shared.Modals,
  noCloseButton: false,
  sticky: false,
})
// biome-ignore lint/style/useComponentExportOnlyModules: Redundant
export const useDialogContext = () => use(dialogContext)

export const DialogRoot: FC<DialogProps & { name: Shared.Modals; noCloseButton?: boolean; sticky?: boolean }> = ({
  name,
  noCloseButton = false,
  sticky = false,
  ...props
}) => {
  const isModalOpen = use$(() => ui$.isModalOpen(name))

  return (
    <dialogContext.Provider value={{ name, noCloseButton, sticky }}>
      <Root {...props} open={isModalOpen} onOpenChange={open => ui$.onChangeModal(name, open)} />
    </dialogContext.Provider>
  )
}

export const DialogOverlay: FC<DialogOverlayProps> = ({ className, style = {}, ...props }) => {
  const { name } = useDialogContext()
  const indexOfTargetModal = use$(() => (ui$.modal.get() || []).indexOf(name) + 1)
  const isLast = use$(() => indexOfTargetModal === (ui$.modal.get() || []).length)
  const zIndex = Math.max(indexOfTargetModal * 100, 100) + 1

  // TODO: animation

  return (
    <Overlay
      className={clsx(
        'fixed inset-0', // positioning
        {
          'bg-black/20 backdrop-blur-sm': isLast,
        },
        'data-[state=open]:animate-fade-in', // animate
        className,
      )}
      style={{
        ...style,

        zIndex,
      }}
      {...props}
    />
  )
}

export const DialogContent: FC<
  DialogContentProps & {
    sticky?: boolean
    ref?: React.Ref<HTMLDivElement>
    header?: React.ReactNode
    footer?: React.ReactNode
  }
> = ({ ref, className, style = {}, children, header = null, footer = null, ...props }) => {
  const { name, sticky } = useDialogContext()
  const indexOfTargetModal = use$(() => (ui$.modal.get() || []).indexOf(name) + 1)
  const zIndex = Math.max(indexOfTargetModal * 100, 100) + 1

  return (
    <Content
      className={clsx(
        'fixed inset-0 flex flex-col', // positioning (common)
        'max-sm:top-20 max-sm:max-h-[calc(100vh-2.5rem)]', // positioning (mobile)
        'sm:-translate-x-1/2 sm:-translate-y-1/2 sm:top-1/2 sm:left-1/2 sm:max-h-screen', // positioning (desktop)
        'bg-white outline-none sm:rounded-b2', // styling
        'data-[state=open]:animate-fade-in-scale-to-top', // animate
        {
          'scrollbar-b2 overflow-y-auto': !sticky, // scroll
        },
        className,
      )}
      style={{
        zIndex,
        ...style,
      }}
      {...props}>
      {header}

      <section
        ref={ref}
        className={clsx(
          'relative w-full',
          'flex flex-col p-8', // centering & padding
          {
            'scrollbar-b2 h-full overflow-y-auto': sticky, // scroll
            'h-auto': !sticky,
          },
        )}>
        {children}
      </section>

      {footer}
    </Content>
  )
}

export const DialogHeader: FC<
  React.HTMLAttributes<HTMLDivElement> & {
    icon?: ReactNode
    title?: string
    description?: string
    disabled?: boolean
  }
> = ({ icon = null, className, title, description, disabled = false, children, ...props }) => {
  const { noCloseButton } = useDialogContext()

  const titleStyles = useTitleStyles({
    variant: 'h4',
  })

  const descriptionStyles = useTextStyles({
    variant: 'dim-2',
  })

  return (
    <section
      className={clsx(
        'flex flex-col gap-0',
        'px-8 pt-8 pb-8', // sizing
        !icon && 'border-accessory-1 border-b', // accessory
        className,
      )}
      {...props}>
      <div className={clsx('flex items-center', { 'gap-12': icon, 'gap-4': !icon })}>
        {icon && (
          <div
            className={clsx(
              'relative', // positioning
              'flex items-center justify-center', // flex
              'rounded-max bg-[#FAFAFA]', // styling
              'p-6', // spacing
            )}>
            {icon}

            <span
              className={clsx(
                '-translate-x-1/2 -translate-y-1/2 absolute top-1/2 left-1/2', // positioning
                'h-40 w-40', // sizing
                'rounded-full border border-[#E9EAEB]', // border
                '-z-10 pointer-events-none',
              )}
            />

            <span
              className={clsx(
                '-translate-x-1/2 -translate-y-1/2 absolute top-1/2 left-1/2', // positioning
                'h-[160px] w-[160px]', // sizing
                'rounded-full border border-[#E9EAEB]/80', // border
                '-z-10 pointer-events-none',
              )}
            />

            <span
              className={clsx(
                '-translate-x-1/2 -translate-y-1/2 absolute top-1/2 left-1/2', // positioning
                'h-[240px] w-[240px]', // sizing
                'rounded-full border border-[#E9EAEB]/60', // border
                '-z-10 pointer-events-none',
              )}
            />

            <span
              className={clsx(
                '-translate-x-1/2 -translate-y-1/2 absolute top-1/2 left-1/2', // positioning
                'h-[320px] w-[320px]', // sizing
                'rounded-full border border-[#E9EAEB]/40', // border
                '-z-10 pointer-events-none',
              )}
            />

            <span
              className={clsx(
                '-translate-x-1/2 -translate-y-1/2 absolute top-1/2 left-1/2', // positioning
                'h-[400px] w-[400px]', // sizing
                'rounded-full border border-[#E9EAEB]/20', // border
                '-z-10 pointer-events-none',
              )}
            />
          </div>
        )}
        <Title className={titleStyles}> {title} </Title>
      </div>
      <Description className={descriptionStyles}> {description} </Description>
      {children}

      {!noCloseButton && <DialogClose position='top-right' disabled={disabled} />}
    </section>
  )
}

export const DialogFooter: FC<
  React.HTMLAttributes<HTMLDivElement> & {
    title?: string
    description?: string
    disabled?: boolean
    slim?: boolean
    borderLess?: boolean
  }
> = ({ className, title, description, disabled = false, slim = true, borderLess = false, children, ...props }) => {
  return (
    <section
      className={clsx(
        'mt-auto flex flex-col gap-0',
        slim ? 'p-4' : 'p-8', // sizing
        !borderLess && 'border-accessory-1 border-t', // accessory
        className,
      )}
      {...props}>
      {children}
    </section>
  )
}

export const DialogClose: FC<DialogCloseProps & { position?: 'none' | 'top-right' }> = ({
  position = 'none',
  className,
  ...props
}) => {
  const buttonStyles = useButtonStyles({
    variant: 'custom',
    className: clsx(
      'p-2',
      {
        'absolute top-8 right-8': position === 'top-right',
      },
      className,
    ),
  })

  return (
    <Close className={buttonStyles} {...props}>
      <XClose className='h-10 w-10' />
    </Close>
  )
}
// biome-ignore lint/style/useComponentExportOnlyModules: Redundant
export const DialogPortal = Portal
