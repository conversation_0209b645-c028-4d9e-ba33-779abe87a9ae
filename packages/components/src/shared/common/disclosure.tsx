import { Disclosure as $Disclosure, DisclosureButton, DisclosurePanel } from '@headlessui/react'

import clsx from 'clsx'
import type { FC, HTMLAttributes, ReactNode } from 'react'
import { useLinkStyles } from './link'

export interface DisclosureStylesProps {
  variant?: 'navigation' | undefined
  className?: string | undefined
}

// biome-ignore lint/style/useComponentExportOnlyModules: Redundant
export const useDisclosureStyles = ({ variant, className }: DisclosureStylesProps) =>
  clsx(
    'cursor-pointer', // styling
    {
      'w-full': variant === 'navigation', // sizing
      'aria-expanded:bg-black/5 hover:text-dim-1 text-left text-nowrap': variant === 'navigation', // other
    },
    // biome-ignore lint/correctness/useHookAtTopLevel: Redundant
    variant === 'navigation' && useLinkStyles({ variant: 'navigation' }),
    className,
  )

export const Disclosure: FC<
  HTMLAttributes<HTMLDivElement> &
    DisclosureStylesProps & {
      label: FC<{ open: boolean }>
      children?: ReactNode
      withBorder?: boolean
      defaultOpen?: boolean
    }
> = ({ label: Label, children, variant, className, withBorder = true, ...props }) => {
  const styles = useDisclosureStyles({ variant, className })

  return (
    <$Disclosure as='div' {...props}>
      {({ open }) => (
        <>
          <DisclosureButton className={styles}>{<Label open={open} />}</DisclosureButton>
          <DisclosurePanel
            transition
            className={clsx(
              'flex flex-col gap-2', // flex
              'ml-8 py-4 pl-4', // margin
              {
                'border-accessory-1 border-l': withBorder,
              },
              'data-closed:-translate-y-6 origin-top transition duration-200 ease-out data-closed:opacity-0', // "animation"
            )}>
            {children}
          </DisclosurePanel>
        </>
      )}
    </$Disclosure>
  )
}
