import { Popover as $Popover, PopoverButton, PopoverPanel } from '@headlessui/react'

import clsx from 'clsx'
import { AnimatePresence } from 'motion/react'
import { div } from 'motion/react-client'
import type { FC, HTMLAttributes, ReactNode } from 'react'
import { type ButtonStylesProps, useButtonStyles } from './button'

export interface PopoverStylesProps {
  variant?: ButtonStylesProps['variant']
  className?: string | undefined
}

// biome-ignore lint/style/useComponentExportOnlyModules: Redundant
export const usePopoverStyles = ({ variant, className }: PopoverStylesProps) =>
  clsx(
    'cursor-pointer', // styling
    {
      'w-full': variant === 'bordered', // sizing
      'aria-expanded:bg-black/5 hover:text-dim-1': variant === 'bordered', // other
      'aria-expanded:bg-primary/90 rounded-c1': variant === 'primary',
    },
    useButtonStyles({ variant }),
    className,
  )

export const Popover: FC<
  HTMLAttributes<HTMLDivElement> &
    PopoverStylesProps & {
      buttonContent: FC<{ open: boolean }>
      children?: ReactNode
      popoverWidth?: string | number
      popoverHeight?: string | number
      popoverPosition?: 'top' | 'bottom' | 'bottom end'
      popoverUnstyled?: boolean
    }
> = ({
  buttonContent: ButtonContent,
  className,
  children,
  variant = 'bordered',
  popoverHeight,
  popoverWidth,
  popoverPosition = 'top',
  popoverUnstyled,
  ...props
}) => {
  const styles = usePopoverStyles({ variant })

  return (
    <$Popover className={clsx('relative w-full', className)} {...props}>
      {({ open }) => (
        <>
          <PopoverButton className={styles}>
            <ButtonContent open={open} />
          </PopoverButton>
          <AnimatePresence>
            {open && (
              <PopoverPanel
                static
                as={div}
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.95 }}
                anchor={popoverPosition}
                className={clsx(
                  'flex origin-top flex-col items-baseline justify-center', // flex
                  'rounded-c1 bg-white shadow-layer-1',
                  !popoverUnstyled && 'px-8 py-4',
                  !popoverUnstyled && 'rounded-c1 border border-accessory-1',
                  '[--anchor-gap:4px] sm:[--anchor-gap:6px]',
                  !popoverUnstyled && {
                    'w-auto min-w-[200px] max-w-screen': !popoverWidth,
                    'h-auto max-h-screen min-h-[50px]': !popoverHeight,
                  },
                )}
                style={{
                  ...(popoverWidth ? { width: popoverWidth } : {}),
                  ...(popoverHeight ? { height: popoverHeight } : {}),
                }}>
                {children}
              </PopoverPanel>
            )}
          </AnimatePresence>
        </>
      )}
    </$Popover>
  )
}
