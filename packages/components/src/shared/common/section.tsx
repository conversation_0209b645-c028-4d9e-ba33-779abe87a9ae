import clsx from 'clsx'
import type { <PERSON> } from 'react'

import { Text } from './text'

export const Section: FC<
  React.HTMLAttributes<HTMLDivElement> & {
    label: string
    placement?: 'same-level' | 'nested' | 'outside'
  }
> = ({ label, children, className, placement = 'same-level', ...props }) => {
  return (
    <section
      className={clsx(
        'px-4',
        {
          'w-full pr-0': placement === 'outside',
        },
        className,
      )}
      {...props}>
      <Text el='legend' variant='dim-3'>
        {label}
      </Text>

      <div
        className={clsx(
          'flex flex-col gap-2', // flex
          'mt-4', // margin & padding
          {
            'pl-4': placement === 'nested',
            '-ml-4': placement === 'outside',
          },
        )}>
        {children}
      </div>
    </section>
  )
}
