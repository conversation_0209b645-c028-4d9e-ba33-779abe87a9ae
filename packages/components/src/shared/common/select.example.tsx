import type { FC } from 'react'
import { Select, SelectContent, SelectItem, SelectSeparator, SelectTrigger } from './select'

// Basit kullanım örneği
export const BasicSelectExample: FC = () => {
  const options = [
    { value: 'apple', label: 'Apple' },
    { value: 'banana', label: 'Banana' },
    { value: 'orange', label: 'Orange' },
    { value: 'grape', label: 'Grape', disabled: true },
  ]

  return (
    <div className="space-y-4">
      <h3>Basic Select with Options Array</h3>
      <Select
        placeholder="Select a fruit..."
        options={options}
        onValueChange={(value) => console.log('Selected:', value)}
      />
    </div>
  )
}

// Manuel children kullanımı
export const ManualSelectExample: FC = () => {
  return (
    <div className="space-y-4">
      <h3>Manual Select with Custom Children</h3>
      <Select onValueChange={(value) => console.log('Selected:', value)}>
        <SelectTrigger placeholder="Choose an option..." />
        <SelectContent>
          <SelectItem value="option1">Option 1</SelectItem>
          <SelectItem value="option2">Option 2</SelectItem>
          <SelectSeparator />
          <SelectItem value="option3">Option 3</SelectItem>
          <SelectItem value="option4" disabled>
            Option 4 (Disabled)
          </SelectItem>
        </SelectContent>
      </Select>
    </div>
  )
}

// Farklı variant ve size örnekleri
export const VariantSelectExamples: FC = () => {
  const options = [
    { value: 'small', label: 'Small Size' },
    { value: 'medium', label: 'Medium Size' },
    { value: 'large', label: 'Large Size' },
  ]

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h4>Default Variant - Small Size</h4>
        <Select
          variant="default"
          size="sm"
          placeholder="Small select..."
          options={options}
        />
      </div>

      <div className="space-y-2">
        <h4>Bordered Variant - Medium Size</h4>
        <Select
          variant="bordered"
          size="md"
          placeholder="Medium select..."
          options={options}
        />
      </div>

      <div className="space-y-2">
        <h4>Error Variant - Large Size</h4>
        <Select
          variant="error"
          size="lg"
          placeholder="Large select with error..."
          options={options}
        />
      </div>
    </div>
  )
}

// Tam örnek kullanım
export const CompleteSelectExample: FC = () => {
  return (
    <div className="max-w-md space-y-8 p-6">
      <BasicSelectExample />
      <ManualSelectExample />
      <VariantSelectExamples />
    </div>
  )
}
