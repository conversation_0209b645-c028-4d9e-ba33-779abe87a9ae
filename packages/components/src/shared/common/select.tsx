import { ChevronDownIcon } from '@mass/icons'
import {
  Content,
  Icon,
  Item,
  ItemIndicator,
  ItemText,
  Portal,
  Root,
  ScrollDownButton,
  ScrollUpButton,
  type SelectContentProps,
  type SelectItemProps,
  type SelectProps,
  type SelectTriggerProps,
  Separator,
  Trigger,
  Value,
  Viewport,
} from '@radix-ui/react-select'
import clsx from 'clsx'
import type { FC, ReactNode } from 'react'

export interface SelectStylesProps {
  variant?: 'default' | 'bordered' | 'error' | undefined
  size?: 'sm' | 'md' | 'lg' | undefined
  className?: string | undefined
}

// biome-ignore lint/style/useComponentExportOnlyModules: Redundant
export const useSelectStyles = ({ variant = 'default', size = 'md', className }: SelectStylesProps) =>
  clsx(
    'flex items-center justify-between gap-2',
    'cursor-pointer rounded-b1', // styling
    'outline-primary',
    'text-black text-xs', // text
    'transition-colors duration-200', // animation
    'disabled:cursor-not-allowed disabled:opacity-80',
    'bg-white border',
    {
      // Size variants
      'px-3 py-2 text-xs': size === 'sm',
      'px-4 py-3 text-sm': size === 'md',
      'px-5 py-4 text-base': size === 'lg',
      // Style variants
      'border-accessory-1 hover:border-primary focus:border-primary': variant === 'default',
      'border-accessory-1 hover:bg-black/5': variant === 'bordered',
      'border-error text-error': variant === 'error',
    },
    className,
  )

// biome-ignore lint/style/useComponentExportOnlyModules: Redundant
export const useSelectContentStyles = ({ className }: { className?: string | undefined }) =>
  clsx('overflow-hidden rounded-b1 bg-white shadow-layer-1', 'border border-accessory-1', 'z-50', className)

// biome-ignore lint/style/useComponentExportOnlyModules: Redundant
export const useSelectItemStyles = ({ className }: { className?: string | undefined }) =>
  clsx(
    'relative flex cursor-pointer select-none items-center',
    'px-4 py-2 text-sm',
    'outline-none',
    'data-[disabled]:pointer-events-none data-[disabled]:opacity-50',
    'data-[highlighted]:bg-primary/10 data-[highlighted]:text-primary',
    className,
  )

export interface SelectOption {
  value: string
  label: string
  disabled?: boolean | undefined
}

export const SelectTrigger: FC<
  SelectTriggerProps &
    SelectStylesProps & {
      placeholder?: string | undefined
      children?: ReactNode
    }
> = ({ variant, size, className, placeholder, children, ...props }) => {
  const styles = useSelectStyles({ variant, size, className })

  return (
    <Trigger className={styles} {...props}>
      <Value placeholder={placeholder}>{children}</Value>
      <Icon asChild>
        <ChevronDownIcon className='h-4 w-4' />
      </Icon>
    </Trigger>
  )
}

export const SelectContent: FC<
  SelectContentProps & {
    className?: string | undefined
  }
> = ({ className, children, ...props }) => {
  const styles = useSelectContentStyles({ className })

  return (
    <Portal>
      <Content className={styles} {...props}>
        <ScrollUpButton className='flex cursor-default items-center justify-center py-1'>
          <ChevronDownIcon className='h-3 w-3 rotate-180' />
        </ScrollUpButton>
        <Viewport className='p-1'>{children}</Viewport>
        <ScrollDownButton className='flex cursor-default items-center justify-center py-1'>
          <ChevronDownIcon className='h-3 w-3' />
        </ScrollDownButton>
      </Content>
    </Portal>
  )
}

export const SelectItem: FC<
  SelectItemProps & {
    className?: string | undefined
    children: ReactNode
  }
> = ({ className, children, ...props }) => {
  const styles = useSelectItemStyles({ className })

  return (
    <Item className={styles} {...props}>
      <ItemText>{children}</ItemText>
      <ItemIndicator className='absolute right-2 flex h-3.5 w-3.5 items-center justify-center'>
        <ChevronDownIcon className='h-3 w-3 rotate-180' />
      </ItemIndicator>
    </Item>
  )
}

export const SelectSeparator: FC<{ className?: string | undefined }> = ({ className }) => (
  <Separator className={clsx('my-1 h-px bg-accessory-1', className)} />
)

export const Select: FC<
  SelectProps &
    SelectStylesProps & {
      placeholder?: string | undefined
      options?: SelectOption[] | undefined
      children?: ReactNode
    }
> = ({ variant, size, className, placeholder, options, children, ...props }) => {
  return (
    <Root {...props}>
      <SelectTrigger variant={variant} size={size} className={className} placeholder={placeholder} />
      <SelectContent>
        {options
          ? options.map(option => (
              <SelectItem key={option.value} value={option.value} {...(option.disabled === true && { disabled: true })}>
                {option.label}
              </SelectItem>
            ))
          : children}
      </SelectContent>
    </Root>
  )
}
