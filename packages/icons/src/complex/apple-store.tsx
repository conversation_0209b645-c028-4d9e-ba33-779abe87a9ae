import type { FC, SVGProps } from 'react'

export const AppleStore: FC<SVGProps<SVGSVGElement>> = props => (
  <svg xmlns='http://www.w3.org/2000/svg' width='190' height='64' viewBox='0 0 190 64' fill='none' {...props}>
    <title> Apple Store </title>
    <g clipPath='url(#a)'>
      <path
        fill='#fff'
        d='M174.87 0H15.139c-.582 0-1.157 0-1.738.003-.486.003-.969.013-1.46.02-1.066.013-2.13.108-3.181.283a10.226 10.226 0 0 0-5.588 2.89A10.01 10.01 0 0 0 1.3 5.788a10.619 10.619 0 0 0-.992 3.045 20.944 20.944 0 0 0-.285 3.204c-.015.49-.016.982-.024 1.473v36.983c.008.497.01.978.024 1.475.013 1.073.108 2.144.285 3.203.174 1.06.509 2.088.992 3.047a9.93 9.93 0 0 0 1.87 2.583 9.968 9.968 0 0 0 2.57 1.886c.951.486 1.968.827 3.019 1.009 1.052.174 2.116.269 3.182.283.49.01.973.017 1.459.017.58.003 1.156.003 1.738.003H174.87c.57 0 1.15 0 1.721-.003.484 0 .98-.006 1.464-.017a20.923 20.923 0 0 0 3.175-.283 10.751 10.751 0 0 0 3.03-1.01 9.992 9.992 0 0 0 2.568-1.885 10.227 10.227 0 0 0 1.876-2.583c.479-.96.811-1.987.983-3.047.177-1.059.275-2.13.294-3.203.007-.497.007-.978.007-1.475.012-.581.012-1.16.012-1.75V15.258c0-.586 0-1.167-.012-1.747 0-.49 0-.983-.007-1.473a21.764 21.764 0 0 0-.294-3.204 10.66 10.66 0 0 0-.983-3.045 10.31 10.31 0 0 0-4.444-4.48 10.69 10.69 0 0 0-3.03-1.003 20.554 20.554 0 0 0-3.175-.283c-.484-.007-.98-.017-1.464-.02C176.02 0 175.44 0 174.87 0Z'
      />
      <path
        fill='#000'
        d='M13.409 62.6c-.484 0-.956-.006-1.436-.017-.995-.013-1.986-.1-2.968-.261a9.293 9.293 0 0 1-2.63-.877 8.588 8.588 0 0 1-2.219-1.626 8.51 8.51 0 0 1-1.62-2.235 9.204 9.204 0 0 1-.862-2.651 20.01 20.01 0 0 1-.265-3c-.01-.338-.023-1.461-.023-1.461V13.51s.014-1.106.024-1.431c.012-1.004.1-2.005.262-2.996a9.259 9.259 0 0 1 .863-2.659 8.595 8.595 0 0 1 1.612-2.237 8.84 8.84 0 0 1 2.227-1.636 9.196 9.196 0 0 1 2.625-.87c.985-.162 1.98-.25 2.978-.262l1.432-.02h163.174l1.45.02a19.52 19.52 0 0 1 2.951.26 9.392 9.392 0 0 1 2.653.877 8.92 8.92 0 0 1 3.834 3.872c.414.831.7 1.72.85 2.638.165.999.257 2.008.276 3.02.004.453.004.94.004 1.424.013.6.013 1.171.013 1.747v33.486c0 .581 0 1.148-.013 1.72 0 .52 0 .997-.006 1.488a20.467 20.467 0 0 1-.271 2.965 9.2 9.2 0 0 1-.858 2.672 8.746 8.746 0 0 1-1.612 2.217 8.603 8.603 0 0 1-2.222 1.636 9.26 9.26 0 0 1-2.648.88c-.982.161-1.974.249-2.968.26-.465.012-.952.018-1.425.018l-1.721.003L13.409 62.6Z'
      />
      <path
        fill='#fff'
        d='M39.327 32.481a7.964 7.964 0 0 1 1.024-3.806 7.891 7.891 0 0 1 2.718-2.837 8.064 8.064 0 0 0-2.764-2.481 7.995 7.995 0 0 0-3.573-.971c-2.666-.282-5.251 1.607-6.61 1.607-1.385 0-3.477-1.58-5.73-1.533a8.392 8.392 0 0 0-4.121 1.24 8.476 8.476 0 0 0-2.98 3.125c-3.071 5.357-.78 13.23 2.161 17.561 1.472 2.121 3.192 4.49 5.442 4.406 2.203-.092 3.025-1.415 5.684-1.415 2.633 0 3.405 1.415 5.701 1.361 2.363-.038 3.852-2.13 5.273-4.27a17.596 17.596 0 0 0 2.41-4.948 7.606 7.606 0 0 1-3.368-2.817 7.694 7.694 0 0 1-1.267-4.222ZM34.99 19.537a7.836 7.836 0 0 0 1.77-5.585 7.853 7.853 0 0 0-5.093 2.656 7.433 7.433 0 0 0-1.44 2.507 7.471 7.471 0 0 0-.376 2.871 6.466 6.466 0 0 0 2.849-.629 6.516 6.516 0 0 0 2.29-1.82ZM67.166 43.423h-7.515l-1.805 5.37h-3.183l7.118-19.868h3.308l7.118 19.869H68.97l-1.804-5.37Zm-6.737-2.478h5.957L63.45 32.23h-.083l-2.938 8.715ZM87.581 41.552c0 4.501-2.39 7.393-5.999 7.393a4.843 4.843 0 0 1-2.623-.612 4.893 4.893 0 0 1-1.9-1.922h-.068v7.175H74.04V34.308h2.856v2.41h.055a5.118 5.118 0 0 1 1.937-1.918 5.068 5.068 0 0 1 2.64-.644c3.648 0 6.053 2.907 6.053 7.396Zm-3.033 0c0-2.933-1.504-4.861-3.799-4.861-2.254 0-3.77 1.968-3.77 4.86 0 2.92 1.516 4.874 3.77 4.874 2.295 0 3.8-1.914 3.8-4.873ZM103.403 41.552c0 4.501-2.391 7.393-6 7.393a4.843 4.843 0 0 1-2.622-.612 4.893 4.893 0 0 1-1.9-1.922h-.069v7.175h-2.95V34.308h2.856v2.41h.054A5.118 5.118 0 0 1 94.71 34.8a5.067 5.067 0 0 1 2.64-.644c3.648 0 6.053 2.907 6.053 7.396Zm-3.033 0c0-2.933-1.504-4.861-3.799-4.861-2.254 0-3.77 1.968-3.77 4.86 0 2.92 1.516 4.874 3.77 4.874 2.295 0 3.799-1.914 3.799-4.873ZM113.86 43.258c.219 1.97 2.118 3.264 4.714 3.264 2.487 0 4.276-1.294 4.276-3.07 0-1.543-1.079-2.466-3.634-3.099l-2.556-.62c-3.62-.881-5.301-2.588-5.301-5.356 0-3.429 2.965-5.783 7.174-5.783 4.167 0 7.023 2.354 7.119 5.782h-2.979c-.178-1.982-1.805-3.18-4.182-3.18-2.377 0-4.003 1.212-4.003 2.974 0 1.405 1.039 2.231 3.58 2.864l2.172.538c4.046.964 5.727 2.601 5.727 5.508 0 3.717-2.939 6.045-7.612 6.045-4.373 0-7.325-2.274-7.516-5.867h3.021ZM132.335 30.88v3.428h2.734v2.355h-2.734v7.985c0 1.241.547 1.82 1.749 1.82.325-.007.649-.03.971-.07v2.341c-.54.102-1.09.148-1.639.138-2.911 0-4.046-1.102-4.046-3.911v-8.303h-2.09v-2.355h2.09V30.88h2.965ZM136.652 41.552c0-4.558 2.664-7.422 6.818-7.422 4.168 0 6.819 2.864 6.819 7.422 0 4.57-2.637 7.421-6.819 7.421-4.181 0-6.818-2.851-6.818-7.421Zm10.631 0c0-3.127-1.422-4.972-3.813-4.972s-3.812 1.86-3.812 4.972c0 3.139 1.421 4.97 3.812 4.97s3.812-1.831 3.812-4.97h.001ZM152.722 34.308h2.814v2.465h.068c.191-.77.638-1.45 1.268-1.927a3.411 3.411 0 0 1 2.19-.69c.34-.001.679.036 1.011.111v2.781a4.094 4.094 0 0 0-1.326-.18 2.958 2.958 0 0 0-2.298.954 3.002 3.002 0 0 0-.777 2.38v8.592h-2.95V34.308ZM173.678 44.539c-.397 2.63-2.939 4.434-6.19 4.434-4.182 0-6.778-2.823-6.778-7.353 0-4.543 2.61-7.49 6.654-7.49 3.977 0 6.478 2.753 6.478 7.145v1.019h-10.153v.18a3.796 3.796 0 0 0 1.015 2.93 3.734 3.734 0 0 0 2.852 1.172 3.232 3.232 0 0 0 1.995-.464 3.267 3.267 0 0 0 1.325-1.573h2.802Zm-9.975-4.323h7.187a3.486 3.486 0 0 0-2.136-3.415 3.432 3.432 0 0 0-1.39-.262 3.618 3.618 0 0 0-2.591 1.07 3.68 3.68 0 0 0-1.07 2.607ZM60.06 13.97a4.162 4.162 0 0 1 3.343 1.308 4.231 4.231 0 0 1 1.115 3.435c0 3.05-1.636 4.803-4.458 4.803h-3.422V13.97h3.422Zm-1.95 8.196h1.785a2.958 2.958 0 0 0 2.373-.967 3.006 3.006 0 0 0 .752-2.466 3.03 3.03 0 0 0-.764-2.45 2.986 2.986 0 0 0-2.36-.964h-1.787v6.847ZM66.18 19.91a3.439 3.439 0 0 1 .865-2.62 3.388 3.388 0 0 1 2.507-1.117 3.364 3.364 0 0 1 2.506 1.117 3.419 3.419 0 0 1 .865 2.62 3.439 3.439 0 0 1-.862 2.626 3.386 3.386 0 0 1-2.51 1.12 3.365 3.365 0 0 1-2.508-1.12 3.42 3.42 0 0 1-.863-2.625Zm5.292 0c0-1.56-.696-2.474-1.918-2.474-1.227 0-1.916.913-1.916 2.475 0 1.574.69 2.48 1.916 2.48 1.222 0 1.918-.912 1.918-2.48ZM81.887 23.516h-1.464l-1.477-5.306h-.112l-1.472 5.306h-1.45l-1.97-7.204h1.431l1.28 5.497h.106l1.47-5.497h1.354l1.47 5.497h.111l1.275-5.497h1.411l-1.963 7.204ZM85.508 16.312h1.358v1.144h.106c.178-.41.48-.755.863-.985.382-.23.826-.335 1.27-.298a2.31 2.31 0 0 1 1.877.74 2.347 2.347 0 0 1 .598 1.94v4.663h-1.411V19.21c0-1.157-.5-1.733-1.543-1.733a1.63 1.63 0 0 0-1.27.517 1.655 1.655 0 0 0-.437 1.309v4.214h-1.411v-7.204ZM93.828 13.5h1.411v10.016h-1.41V13.5ZM97.2 19.91a3.439 3.439 0 0 1 .866-2.62 3.389 3.389 0 0 1 2.507-1.117 3.365 3.365 0 0 1 2.506 1.117 3.437 3.437 0 0 1 .866 2.62 3.433 3.433 0 0 1-.863 2.625 3.37 3.37 0 0 1-5.018 0 3.42 3.42 0 0 1-.863-2.624Zm5.293 0c0-1.56-.697-2.474-1.918-2.474-1.227 0-1.917.913-1.917 2.475 0 1.574.69 2.48 1.917 2.48 1.221 0 1.918-.912 1.918-2.48ZM105.43 21.479c0-1.297.958-2.045 2.659-2.15l1.937-.113v-.622c0-.761-.499-1.19-1.464-1.19-.787 0-1.333.29-1.49.8h-1.366c.144-1.238 1.3-2.031 2.921-2.031 1.793 0 2.804.899 2.804 2.42v4.923h-1.358v-1.012h-.112c-.227.363-.545.66-.922.858a2.39 2.39 0 0 1-1.226.273 2.142 2.142 0 0 1-1.666-.545 2.19 2.19 0 0 1-.717-1.611Zm4.596-.616v-.602l-1.746.112c-.984.067-1.431.404-1.431 1.04 0 .648.558 1.025 1.326 1.025a1.675 1.675 0 0 0 1.257-.392 1.704 1.704 0 0 0 .594-1.183ZM113.285 19.91c0-2.276 1.162-3.718 2.968-3.718a2.357 2.357 0 0 1 2.192 1.264h.106V13.5h1.411v10.017h-1.352v-1.138h-.112a2.47 2.47 0 0 1-2.245 1.257c-1.819 0-2.968-1.442-2.968-3.724Zm1.458 0c0 1.53.715 2.449 1.91 2.449 1.189 0 1.924-.933 1.924-2.442 0-1.501-.742-2.448-1.924-2.448-1.188 0-1.91.926-1.91 2.442ZM125.8 19.91a3.437 3.437 0 0 1 .865-2.62 3.379 3.379 0 0 1 2.506-1.117 3.368 3.368 0 0 1 2.507 1.117 3.425 3.425 0 0 1 .865 2.62 3.428 3.428 0 0 1-.863 2.626 3.383 3.383 0 0 1-2.509 1.12 3.367 3.367 0 0 1-2.508-1.12 3.428 3.428 0 0 1-.863-2.625Zm5.292 0c0-1.56-.697-2.474-1.918-2.474-1.227 0-1.917.913-1.917 2.475 0 1.574.69 2.48 1.917 2.48 1.222 0 1.918-.912 1.918-2.48ZM134.437 16.312h1.358v1.144h.105c.179-.41.481-.755.863-.985a2.13 2.13 0 0 1 1.271-.298 2.31 2.31 0 0 1 1.876.74 2.35 2.35 0 0 1 .599 1.94v4.663h-1.411V19.21c0-1.157-.5-1.733-1.543-1.733a1.628 1.628 0 0 0-1.27.517 1.665 1.665 0 0 0-.437 1.309v4.214h-1.411v-7.204ZM148.482 14.518v1.826h1.549v1.198h-1.549v3.705c0 .755.308 1.085 1.011 1.085.179 0 .359-.012.538-.033v1.185a4.59 4.59 0 0 1-.768.072c-1.569 0-2.194-.556-2.194-1.945v-4.069h-1.135v-1.198h1.135v-1.826h1.413ZM151.958 13.5h1.399v3.97h.111c.188-.415.498-.762.888-.993.39-.23.841-.335 1.292-.298a2.337 2.337 0 0 1 1.858.76 2.374 2.374 0 0 1 .605 1.926v4.651h-1.413v-4.3c0-1.151-.532-1.734-1.529-1.734a1.655 1.655 0 0 0-1.321.491 1.682 1.682 0 0 0-.479 1.336v4.207h-1.411V13.5ZM166.338 21.571a2.926 2.926 0 0 1-1.173 1.61 2.888 2.888 0 0 1-1.925.474 3.228 3.228 0 0 1-2.506-1.088 3.287 3.287 0 0 1-.797-2.63 3.349 3.349 0 0 1 .796-2.64 3.278 3.278 0 0 1 2.501-1.124c1.989 0 3.189 1.37 3.189 3.632v.496h-5.048v.08a1.901 1.901 0 0 0 1.904 2.064c.337.04.679-.02.982-.177.303-.155.553-.398.719-.697h1.358Zm-4.963-2.322h3.611a1.745 1.745 0 0 0-1.058-1.733 1.718 1.718 0 0 0-.702-.133 1.815 1.815 0 0 0-1.316.54 1.84 1.84 0 0 0-.535 1.326Z'
      />
    </g>
    <defs>
      <clipPath id='a'>
        <path fill='#fff' d='M0 0h190v64H0z' />
      </clipPath>
    </defs>
  </svg>
)
