import type { FC, SVGProps } from 'react'

export const GoogleStore: FC<SVGProps<SVGSVGElement>> = props => (
  <svg xmlns='http://www.w3.org/2000/svg' width='215' height='64' viewBox='0 0 215 64' fill='none' {...props}>
    <title> Google Store </title>
    <g clipPath='url(#clip0_6069_77422)'>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M207.034 64H7.96589C3.57555 64 0 60.393 0 56V8C0 3.59102 3.57555 0 7.96589 0H207.034C211.42 0 215 3.59102 215 8V56C215 60.393 211.42 64 207.034 64Z'
        fill='black'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M207.034 1.2808C210.716 1.2808 213.73 4.30125 213.73 8V56C213.73 59.6988 210.736 62.7192 207.034 62.7192H7.96589C4.28355 62.7192 1.26964 59.6988 1.26964 56V8C1.26964 4.30125 4.26377 1.2808 7.96589 1.2808H207.034ZM207.034 0H7.96589C3.57555 0 0 3.60698 0 8V56C0 60.409 3.57555 64 7.96589 64H207.034C211.42 64 215 60.409 215 56V8C215 3.60698 211.42 0 207.034 0Z'
        fill='white'
      />
      <path
        d='M113.262 21.171C111.827 21.171 110.616 20.6642 109.659 19.6667C108.71 18.6972 108.18 17.3605 108.204 15.9959C108.204 14.5396 108.694 13.3066 109.659 12.3291C110.612 11.3316 111.823 10.8248 113.258 10.8248C114.678 10.8248 115.889 11.3316 116.862 12.3291C117.827 13.3386 118.317 14.5715 118.317 15.9959C118.301 17.4563 117.811 18.6892 116.862 19.6628C115.908 20.6682 114.698 21.171 113.262 21.171ZM70.5418 21.171C69.1377 21.171 67.9195 20.6722 66.9228 19.6867C65.934 18.7051 65.4316 17.4642 65.4316 15.9999C65.4316 14.5356 65.934 13.2947 66.9228 12.3131C67.8997 11.3276 69.1179 10.8288 70.5418 10.8288C71.2379 10.8288 71.9143 10.9685 72.5629 11.2518C73.1997 11.5271 73.7179 11.9022 74.1015 12.361L74.1964 12.4767L73.1246 13.5381L73.0138 13.4064C72.4087 12.6802 71.5978 12.3251 70.526 12.3251C69.5688 12.3251 68.7343 12.6682 68.0461 13.3465C67.3539 14.0288 67.0019 14.9226 67.0019 16.0039C67.0019 17.0852 67.3539 17.979 68.0461 18.6613C68.7343 19.3396 69.5688 19.6827 70.526 19.6827C71.5464 19.6827 72.4047 19.3396 73.0732 18.6613C73.4687 18.2623 73.7139 17.7036 73.8009 16.9974H70.3638V15.5051H75.2762L75.296 15.6408C75.3316 15.8922 75.3672 16.1515 75.3672 16.3909C75.3672 17.7675 74.9558 18.8807 74.1411 19.7027C73.2155 20.6762 72.0052 21.171 70.5418 21.171ZM127.343 20.9595H125.828L121.189 13.4702L121.228 14.8189V20.9555H119.713V11.0403H121.442L121.489 11.1161L125.852 18.1705L125.812 16.8258V11.0403H127.343V20.9595ZM101.879 20.9595H100.345V12.5326H97.6826V11.0403H104.537V12.5326H101.875V20.9595H101.879ZM96.4328 20.9595H94.9021V11.0403H96.4328V20.9595ZM87.8301 20.9595H86.2995V12.5326H83.6376V11.0403H90.492V12.5326H87.8301V20.9595ZM82.6686 20.9435H76.799V11.0403H82.6686V12.5326H78.3336V15.2538H82.2453V16.7301H78.3336V19.4513H82.6686V20.9435ZM110.782 18.6413C111.467 19.3316 112.297 19.6787 113.262 19.6787C114.255 19.6787 115.066 19.3396 115.742 18.6413C116.415 17.963 116.755 17.0732 116.755 15.9999C116.755 14.9266 116.415 14.0328 115.746 13.3585C115.062 12.6682 114.227 12.3211 113.266 12.3211C112.273 12.3211 111.463 12.6603 110.79 13.3585C110.118 14.0368 109.778 14.9266 109.778 15.9999C109.778 17.0732 110.114 17.967 110.782 18.6413Z'
        fill='white'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M108.061 34.809C104.324 34.809 101.294 37.6698 101.294 41.616C101.294 45.5262 104.343 48.4229 108.061 48.4229C111.799 48.4229 114.829 45.5422 114.829 41.616C114.829 37.6698 111.799 34.809 108.061 34.809ZM108.061 45.7257C106.016 45.7257 104.256 44.018 104.256 41.6C104.256 39.1461 106.02 37.4743 108.061 37.4743C110.106 37.4743 111.866 39.1461 111.866 41.6C111.87 44.0339 110.106 45.7257 108.061 45.7257ZM93.2963 34.809C89.5586 34.809 86.5288 37.6698 86.5288 41.616C86.5288 45.5262 89.5784 48.4229 93.2963 48.4229C97.034 48.4229 100.064 45.5422 100.064 41.616C100.064 37.6698 97.0301 34.809 93.2963 34.809ZM93.2963 45.7257C91.2514 45.7257 89.4913 44.018 89.4913 41.6C89.4913 39.1461 91.2554 37.4743 93.2963 37.4743C95.3412 37.4743 97.1013 39.1461 97.1013 41.6C97.1013 44.0339 95.3412 45.7257 93.2963 45.7257ZM75.727 36.8878V39.7686H82.5815C82.3719 41.3845 81.8419 42.5776 81.031 43.3955C80.0264 44.409 78.4759 45.5102 75.7428 45.5102C71.5305 45.5102 68.2357 42.0788 68.2357 37.8294C68.2357 33.5801 71.5305 30.1486 75.7428 30.1486C78.0171 30.1486 79.6744 31.0544 80.9045 32.2115L82.9296 30.1686C81.2209 28.5167 78.9466 27.2519 75.7587 27.2519C69.98 27.2519 65.1309 32 65.1309 37.8135C65.1309 43.6429 69.9761 48.3751 75.7587 48.3751C78.8794 48.3751 81.2209 47.3456 83.0719 45.4065C84.9586 43.5032 85.5559 40.818 85.5559 38.6514C85.5559 37.9771 85.5044 37.3706 85.3976 36.8559H75.7231C75.727 36.8519 75.727 36.8878 75.727 36.8878ZM147.59 39.1302C147.025 37.602 145.316 34.793 141.812 34.793C138.339 34.793 135.452 37.5501 135.452 41.6C135.452 45.4224 138.307 48.407 142.148 48.407C145.233 48.407 147.029 46.5037 147.768 45.3865L145.478 43.8384C144.719 44.9756 143.663 45.7217 142.164 45.7217C140.649 45.7217 139.589 45.0274 138.885 43.6589L147.907 39.8883C147.907 39.8923 147.59 39.1302 147.59 39.1302ZM138.39 41.4045C138.319 38.7751 140.415 37.4384 141.914 37.4384C143.097 37.4384 144.082 38.0249 144.418 38.8788L138.39 41.4045ZM131.061 48H134.024V28.002H131.061V48ZM126.196 36.3212H126.089C125.421 35.5192 124.151 34.793 122.53 34.793C119.164 34.793 116.063 37.7816 116.063 41.62C116.063 45.4424 119.148 48.395 122.53 48.395C124.132 48.395 125.421 47.6648 126.089 46.8469H126.196V47.8244C126.196 50.4219 124.82 51.8225 122.601 51.8225C120.785 51.8225 119.658 50.5057 119.199 49.4045L116.624 50.4898C117.364 52.2853 119.338 54.5077 122.597 54.5077C126.07 54.5077 129.012 52.4449 129.012 47.4135V35.2H126.212V36.3212C126.216 36.3212 126.196 36.3212 126.196 36.3212ZM122.795 45.7257C120.75 45.7257 119.041 44.002 119.041 41.62C119.041 39.218 120.75 37.4783 122.795 37.4783C124.82 37.4783 126.39 39.2379 126.39 41.62C126.41 44.002 124.824 45.7257 122.795 45.7257ZM161.461 28.002H154.377V48H157.34V40.4269H161.465C164.744 40.4269 167.968 38.0289 167.968 34.2225C167.968 30.416 164.756 28.002 161.461 28.002ZM161.548 37.6339H157.336V30.7711H161.548C163.767 30.7711 165.021 32.6185 165.021 34.2025C165.021 35.7706 163.751 37.6339 161.548 37.6339ZM179.841 34.7731C177.69 34.7731 175.471 35.7347 174.553 37.8294L177.18 38.9307C177.745 37.8294 178.781 37.4544 179.877 37.4544C181.412 37.4544 182.962 38.3801 182.998 40.0319V40.2434C182.468 39.9402 181.305 39.4773 179.913 39.4773C177.077 39.4773 174.201 41.0414 174.201 43.9741C174.201 46.6594 176.527 48.383 179.118 48.383C181.107 48.383 182.203 47.4773 182.887 46.4279H182.994V47.9761H185.85V40.3152C185.85 36.7481 183.223 34.7731 179.841 34.7731ZM179.47 45.7257C178.501 45.7257 177.144 45.2469 177.144 44.018C177.144 42.4698 178.837 41.8833 180.28 41.8833C181.586 41.8833 182.203 42.1666 182.978 42.5576C182.764 44.3531 181.261 45.7097 179.47 45.7257ZM196.268 35.2L192.866 43.8743H192.759L189.235 35.2H186.047L191.335 47.3257L188.322 54.0648H191.407L199.543 35.2H196.268ZM169.601 48H172.564V28.002H169.601V48Z'
        fill='white'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M16.549 12.0538C16.0902 12.5526 15.8252 13.3147 15.8252 14.3122V49.6917C15.8252 50.6892 16.0902 51.4513 16.5648 51.9301L16.6874 52.0379L36.3372 32.2154V31.7725L16.6716 11.9461L16.549 12.0538Z'
        fill='url(#paint0_linear_6069_77422)'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M42.8747 38.8429L36.3208 32.2314V31.7686L42.8747 25.1571L43.0171 25.2449L50.7694 29.6898C52.9884 30.9506 52.9884 33.0334 50.7694 34.3102L43.0171 38.7551L42.8747 38.8429Z'
        fill='url(#paint1_linear_6069_77422)'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M43.0174 38.7551L36.3212 32L16.5488 51.9461C17.2726 52.7282 18.4869 52.816 19.8436 52.0539L43.0174 38.7551Z'
        fill='url(#paint2_linear_6069_77422)'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M43.0174 25.2449L19.8436 11.9661C18.4869 11.184 17.2687 11.2918 16.5488 12.0738L36.3212 32L43.0174 25.2449Z'
        fill='url(#paint3_linear_6069_77422)'
      />
      <path
        opacity='0.2'
        fillRule='evenodd'
        clipRule='evenodd'
        d='M42.875 38.6116L19.8592 51.8026C18.5737 52.5487 17.4267 52.4968 16.6871 51.8185L16.5645 51.9422L16.6871 52.0499C17.4267 52.7242 18.5737 52.7801 19.8592 52.034L43.0332 38.7552L42.875 38.6116Z'
        fill='black'
      />
      <path
        opacity='0.12'
        fillRule='evenodd'
        clipRule='evenodd'
        d='M50.7699 34.0788L42.8555 38.6115L42.9979 38.7551L50.7502 34.3102C51.8616 33.6718 52.4074 32.8339 52.4074 32C52.3402 32.7661 51.7746 33.4923 50.7699 34.0788Z'
        fill='black'
      />
      <path
        opacity='0.25'
        fillRule='evenodd'
        clipRule='evenodd'
        d='M19.8437 12.1976L50.7699 29.9212C51.7745 30.4918 52.3401 31.238 52.4272 32C52.4272 31.1661 51.8813 30.3282 50.7699 29.6898L19.8437 11.9661C17.6248 10.6853 15.8252 11.7547 15.8252 14.3123V14.5437C15.8252 11.9821 17.6248 10.9327 19.8437 12.1976Z'
        fill='white'
      />
    </g>
    <defs>
      <linearGradient
        id='paint0_linear_6069_77422'
        x1='34.5746'
        y1='13.9305'
        x2='3.06291'
        y2='22.2536'
        gradientUnits='userSpaceOnUse'>
        <stop stopColor='#00A0FF' />
        <stop offset='0.00657' stopColor='#00A1FF' />
        <stop offset='0.2601' stopColor='#00BEFF' />
        <stop offset='0.5122' stopColor='#00D2FF' />
        <stop offset='0.7604' stopColor='#00DFFF' />
        <stop offset='1' stopColor='#00E3FF' />
      </linearGradient>
      <linearGradient
        id='paint1_linear_6069_77422'
        x1='53.6631'
        y1='32.0001'
        x2='15.2852'
        y2='32.0001'
        gradientUnits='userSpaceOnUse'>
        <stop stopColor='#FFE000' />
        <stop offset='0.4087' stopColor='#FFBD00' />
        <stop offset='0.7754' stopColor='#FFA500' />
        <stop offset='1' stopColor='#FF9C00' />
      </linearGradient>
      <linearGradient
        id='paint2_linear_6069_77422'
        x1='39.3763'
        y1='35.6757'
        x2='13.7797'
        y2='78.3816'
        gradientUnits='userSpaceOnUse'>
        <stop stopColor='#FF3A44' />
        <stop offset='1' stopColor='#C31162' />
      </linearGradient>
      <linearGradient
        id='paint3_linear_6069_77422'
        x1='11.5699'
        y1='0.286072'
        x2='22.9897'
        y2='19.3593'
        gradientUnits='userSpaceOnUse'>
        <stop stopColor='#32A071' />
        <stop offset='0.0685' stopColor='#2DA771' />
        <stop offset='0.4762' stopColor='#15CF74' />
        <stop offset='0.8009' stopColor='#06E775' />
        <stop offset='1' stopColor='#00F076' />
      </linearGradient>
      <clipPath id='clip0_6069_77422'>
        <rect width='215' height='64' fill='white' />
      </clipPath>
    </defs>
  </svg>
)
