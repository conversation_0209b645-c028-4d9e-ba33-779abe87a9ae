import type { FC, SVGProps } from 'react'

export const DotsVerticalIcon: FC<SVGProps<SVGSVGElement>> = ({ strokeWidth = '1.66667', ...props }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='20'
    height='20'
    viewBox='0 0 20 20'
    fill='none'
    strokeWidth={strokeWidth}
    {...props}>
    <title> Dots Vertical </title>

    <path
      d='M10.0003 10.8333C10.4606 10.8333 10.8337 10.4602 10.8337 10C10.8337 9.53977 10.4606 9.16667 10.0003 9.16667C9.54009 9.16667 9.16699 9.53977 9.16699 10C9.16699 10.4602 9.54009 10.8333 10.0003 10.8333Z'
      stroke='currentColor'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <path
      d='M10.0003 5C10.4606 5 10.8337 4.62691 10.8337 4.16667C10.8337 3.70643 10.4606 3.33334 10.0003 3.33334C9.54009 3.33334 9.16699 3.70643 9.16699 4.16667C9.16699 4.62691 9.54009 5 10.0003 5Z'
      stroke='currentColor'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <path
      d='M10.0003 16.6667C10.4606 16.6667 10.8337 16.2936 10.8337 15.8333C10.8337 15.3731 10.4606 15 10.0003 15C9.54009 15 9.16699 15.3731 9.16699 15.8333C9.16699 16.2936 9.54009 16.6667 10.0003 16.6667Z'
      stroke='currentColor'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
  </svg>
)
