import type { FC, SVGProps } from 'react'

export const EGovernmentWhite: FC<SVGProps<SVGSVGElement>> = props => (
  <svg xmlns='http://www.w3.org/2000/svg' width='25' height='25' viewBox='0 0 25 25' fill='none' {...props}>
    <title> e-Government </title>
    <g clipPath='url(#a)'>
      <path
        fill='#fff'
        fillRule='evenodd'
        d='M19.887 3.002v.016c2.237.13 4.03.81 4.508 2.753.271 1.1-.027 2.243-.403 3.075-.4.88-.863 1.569-1.415 2.237-1.092 1.323-2.412 2.405-3.872 3.412a23.893 23.893 0 0 1-4.726 2.512 18.53 18.53 0 0 1-2.752.868c-.958.217-2.143.385-3.187.146-.98-.226-1.737-.742-1.882-1.772-.154-1.108.376-2.03.855-2.753.98-1.475 2.336-2.542 3.872-3.413 1.535-.87 3.325-1.598 5.674-1.497-1.917.217-3.229.809-4.509 1.673-1.112.752-2.44 1.879-2.845 3.38-.285 1.053.188 1.756.886 2.094.707.34 1.787.362 2.69.274 1.815-.178 3.353-.852 4.6-1.56 1.936-1.1 3.637-2.594 4.789-4.556.387-.662.738-1.412.902-2.223.445-2.21-.613-3.441-2.131-3.927-1.547-.495-3.812-.241-5.395.193-3.197.879-5.743 2.479-8.007 4.377a21.233 21.233 0 0 0-3.047 3.171c-.921 1.178-1.746 2.466-2.285 3.96-.292.809-.506 1.722-.387 2.703.316 2.627 2.98 3.411 5.97 3.237 3.797-.223 6.662-1.58 9.267-3.058-2.362 1.518-5.22 2.923-8.566 3.476C4.74 22.42.755 21.71.5 18.082v-.643c.257-2.448 1.379-4.244 2.519-5.762 1.216-1.62 2.591-2.894 4.119-4.089a23.095 23.095 0 0 1 5.146-3.027c1.918-.82 4.075-1.434 6.56-1.561h1.043'
        clipRule='evenodd'
      />
    </g>
    <defs>
      <clipPath id='a'>
        <path fill='#fff' d='M.5.5h24v24H.5z' />
      </clipPath>
    </defs>
  </svg>
)
