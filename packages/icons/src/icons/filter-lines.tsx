import type { FC, SVGProps } from 'react'

export const FilterLinesIcon: FC<SVGProps<SVGSVGElement>> = ({ strokeWidth = '2', ...props }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='20'
    height='20'
    viewBox='0 0 24 24'
    fill='none'
    strokeWidth={strokeWidth}
    {...props}>
    <title> Filter Lines </title>

    <path d='M6 12H18M3 6H21M9 18H15' stroke='currentColor' strokeLinecap='round' strokeLinejoin='round' />
  </svg>
)
