import type { FC, SVGProps } from 'react'

export const NotificationIcon: FC<SVGProps<SVGSVGElement>> = props => (
  <svg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 20 20' fill='none' {...props}>
    <title> Notification </title>

    <path
      d='M7.79545 17.5C8.38306 18.0186 9.15493 18.3333 10.0003 18.3333C10.8457 18.3333 11.6175 18.0186 12.2052 17.5M15.0003 6.66666C15.0003 5.34058 14.4735 4.06881 13.5358 3.13113C12.5982 2.19345 11.3264 1.66666 10.0003 1.66666C8.67422 1.66666 7.40245 2.19345 6.46477 3.13113C5.52708 4.06881 5.0003 5.34058 5.0003 6.66666C5.0003 9.24182 4.35069 11.005 3.62502 12.1712C3.01291 13.1549 2.70685 13.6468 2.71807 13.784C2.7305 13.9359 2.76268 13.9938 2.88511 14.0846C2.99568 14.1667 3.49413 14.1667 4.49101 14.1667H15.5096C16.5065 14.1667 17.0049 14.1667 17.1155 14.0846C17.2379 13.9938 17.2701 13.9359 17.2825 13.784C17.2938 13.6468 16.9877 13.1549 16.3756 12.1712C15.6499 11.005 15.0003 9.24182 15.0003 6.66666Z'
      stroke='currentColor'
      strokeWidth='1.67'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
  </svg>
)
