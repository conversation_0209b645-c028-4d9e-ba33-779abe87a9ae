import type { FC, SVGProps } from 'react'

export const PlusIcon: FC<SVGProps<SVGSVGElement>> = ({ strokeWidth = '2', ...props }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='20'
    height='20'
    viewBox='0 0 24 24'
    fill='none'
    strokeWidth={strokeWidth}
    {...props}>
    <title> Plus </title>

    <path id='Icon' d='M12 5V19M5 12H19' stroke='currentColor' strokeLinecap='round' strokeLinejoin='round' />
  </svg>
)
