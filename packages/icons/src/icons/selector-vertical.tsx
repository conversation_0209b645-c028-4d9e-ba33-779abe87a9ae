import type { FC, SVGProps } from 'react'

export const SelectorVerticalIcon: FC<SVGProps<SVGSVGElement>> = props => (
  <svg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 20 20' fill='none' {...props}>
    <title> SelectorVertical </title>

    <path
      d='M5.83301 12.5L9.99967 16.6666L14.1663 12.5M5.83301 7.49998L9.99967 3.33331L14.1663 7.49998'
      stroke='currentColor'
      strokeWidth='1.67'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
  </svg>
)
