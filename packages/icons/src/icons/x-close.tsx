import type { FC, SVGProps } from 'react'

export const XClose: FC<SVGProps<SVGSVGElement>> = props => (
  <svg xmlns='http://www.w3.org/2000/svg' width='24' height='25' viewBox='0 0 24 25' fill='none' {...props}>
    <title> Close </title>

    <path
      d='M18 6.5L6 18.5M6 6.5L18 18.5'
      stroke='#A4A7AE'
      strokeWidth='2'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
  </svg>
)
