import type { FC, SVGProps } from 'react'

export const LogoFull: FC<SVGProps<SVGSVGElement>> = props => (
  <svg xmlns='http://www.w3.org/2000/svg' width='151' height='48' viewBox='0 0 151 48' fill='none' {...props}>
    <title>MASS full logo</title>
    <g filter='url(#filter0_i_35_691)'>
      <g clipPath='url(#clip0_35_691)'>
        <rect width='48' height='48' rx='12' fill='#2E90FA' />
        <rect width='48' height='48' fill='url(#paint0_linear_35_691)' />
        <path d='M14.0258 11H18.0516V21.5882L14.0258 26.9115H10V16.3233L14.0258 11Z' fill='#84CAFF' />
        <path d='M18.0519 11L14.0261 16.3233V26.9115L18.0519 21.5882V11Z' fill='#B2DDFF' />
        <path d='M14.0258 16.3233H10L14.0258 11H18.0516L14.0258 16.3233Z' fill='#53B1FD' />
        <path d='M23.9224 11H27.9482V28.2979L23.9224 33.6452H19.8965V16.3233L23.9224 11Z' fill='#84CAFF' />
        <path d='M27.9485 11L23.9227 16.3233V33.5613L27.9485 28.3071V11Z' fill='#B2DDFF' />
        <path d='M23.9224 16.3233H19.8965L23.9224 11H27.9482L23.9224 16.3233Z' fill='#53B1FD' />
        <path d='M33.8194 11H37.8452V31.6527L33.8194 37H29.7936V16.3233L33.8194 11Z' fill='#84CAFF' />
        <path d='M33.8194 16.3233H29.7936L33.8194 11H37.8452L33.8194 16.3233Z' fill='#53B1FD' />
        <path d='M37.8449 31.6615V11L33.8191 16.3223V37L37.8449 31.6615Z' fill='#B2DDFF' />
      </g>
      <rect x='1' y='1' width='46' height='46' rx='11' stroke='url(#paint1_linear_35_691)' strokeWidth='2' />
    </g>
    <path
      d='M81.9039 14.2588C85.8373 14.2588 89.0248 17.4399 89.0248 21.3656V33.6996H85.5321V21.3656C85.5321 19.3689 83.9045 17.7445 81.9039 17.7445C79.9033 17.7445 78.2417 19.3689 78.2417 21.3656V33.6996H74.7491V21.3656C74.7491 19.3689 73.1215 17.7445 71.1209 17.7445C69.1202 17.7445 67.4587 19.3689 67.4587 21.3656V33.6996H64V14.251H67.4587V15.274C68.5438 14.631 69.7984 14.2588 71.1209 14.2588C73.2571 14.2588 75.1899 15.2064 76.5124 16.7292C77.8348 15.2064 79.7337 14.2588 81.9039 14.2588Z'
      fill='#2E90FA'
    />
    <path
      d='M104.341 14.3193H107.834V33.7488H104.341V32.622C103.358 33.1 102.273 33.4073 101.12 33.4073H99.1197C94.7795 33.4073 91.2531 29.8219 91.2531 25.4511V22.173C91.2531 17.8022 94.7795 14.251 99.1197 14.251H101.425C102.443 14.251 103.46 14.49 104.341 14.8998V14.3193ZM104.341 27.8414V19.5437C103.765 18.451 102.646 17.7681 101.425 17.7681H99.1197C96.7122 17.7681 94.7117 19.7486 94.7117 22.173V25.4511C94.7117 27.9097 96.7122 29.8902 99.1197 29.8902H101.12C102.51 29.8902 103.731 29.1048 104.341 27.8414Z'
      fill='#2E90FA'
    />
    <path
      d='M124.137 26.0824C124.711 28.1178 124.036 30.5262 122.516 31.917C121.03 33.2739 119.071 33.7488 117.045 33.7488C114.783 33.7488 112.419 33.1382 110.392 32.4258L111.54 29.1354C115.593 30.5601 118.734 30.628 120.152 29.305C120.726 28.7962 120.996 27.7785 120.794 26.9983C120.625 26.3199 120.22 26.1842 119.916 26.1164C119.443 26.0146 118.768 25.9128 118.058 25.845C115.593 25.5397 112.486 25.1666 110.899 22.9616C109.751 21.3673 109.784 19.1624 111 17.161C112.79 14.2098 117.754 13.4296 123.968 15.1596L123.056 18.5179C118.16 17.161 114.715 17.7377 113.972 18.9928C113.634 19.5694 113.364 20.4175 113.736 20.9263C114.411 21.9101 116.775 22.1814 118.464 22.385C119.274 22.4867 119.983 22.5546 120.659 22.6903C122.415 23.0634 123.664 24.3185 124.137 26.0824Z'
      fill='#2E90FA'
    />
    <path
      d='M140.795 26.0824C141.375 28.1178 140.692 30.5262 139.156 31.917C137.654 33.2739 135.675 33.7488 133.627 33.7488C131.34 33.7488 128.95 33.1382 126.902 32.4258L128.063 29.1354C132.159 30.5601 135.333 30.628 136.767 29.305C137.347 28.7962 137.62 27.7785 137.415 26.9983C137.245 26.3199 136.835 26.1842 136.528 26.1164C136.05 26.0146 135.367 25.9128 134.651 25.845C132.159 25.5397 129.018 25.1666 127.414 22.9616C126.254 21.3673 126.288 19.1624 127.517 17.161C129.326 14.2098 134.343 13.4296 140.624 15.1596L139.702 18.5179C134.753 17.161 131.271 17.7377 130.52 18.9928C130.179 19.5694 129.906 20.4175 130.281 20.9263C130.964 21.9101 133.353 22.1814 135.06 22.385C135.879 22.4867 136.596 22.5546 137.279 22.6903C139.054 23.0634 140.317 24.3185 140.795 26.0824Z'
      fill='#2E90FA'
    />
    <defs>
      <filter
        id='filter0_i_35_691'
        x='0'
        y='-3'
        width='48'
        height='51'
        filterUnits='userSpaceOnUse'
        colorInterpolationFilters='sRGB'>
        <feFlood floodOpacity='0' result='BackgroundImageFix' />
        <feBlend mode='normal' in='SourceGraphic' in2='BackgroundImageFix' result='shape' />
        <feColorMatrix
          in='SourceAlpha'
          type='matrix'
          values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
          result='hardAlpha'
        />
        <feOffset dy='-3' />
        <feGaussianBlur stdDeviation='1.5' />
        <feComposite in2='hardAlpha' operator='arithmetic' k2='-1' k3='1' />
        <feColorMatrix type='matrix' values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0' />
        <feBlend mode='normal' in2='shape' result='effect1_innerShadow_35_691' />
      </filter>
      <linearGradient id='paint0_linear_35_691' x1='24' y1='5.96047e-07' x2='26' y2='48' gradientUnits='userSpaceOnUse'>
        <stop stopColor='white' stopOpacity='0' />
        <stop offset='1' stopColor='white' stopOpacity='0.12' />
      </linearGradient>
      <linearGradient id='paint1_linear_35_691' x1='24' y1='0' x2='24' y2='48' gradientUnits='userSpaceOnUse'>
        <stop stopColor='white' stopOpacity='0.12' />
        <stop offset='1' stopColor='white' stopOpacity='0' />
      </linearGradient>
      <clipPath id='clip0_35_691'>
        <rect width='48' height='48' rx='12' fill='white' />
      </clipPath>
    </defs>
  </svg>
)
