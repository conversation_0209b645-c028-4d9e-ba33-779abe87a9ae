import type { FC, SVGProps } from 'react'

export const Logo: FC<SVGProps<SVGSVGElement>> = props => (
  <svg xmlns='http://www.w3.org/2000/svg' width='144' height='48' viewBox='0 0 48 48' fill='none' {...props}>
    <title>MASS logo</title>
    <g filter='url(#a)'>
      <g clipPath='url(#b)'>
        <rect width={48} height={48} fill='#2E90FA' rx={12} />
        <path fill='url(#c)' d='M0 0h48v48H0z' />
        <path fill='#84CAFF' d='M14.026 11h4.026v10.588l-4.026 5.323H10V16.324L14.026 11Z' />
        <path fill='#B2DDFF' d='m18.052 11-4.026 5.323v10.588l4.026-5.323V11Z' />
        <path fill='#53B1FD' d='M14.026 16.323H10L14.026 11h4.026l-4.026 5.323Z' />
        <path fill='#84CAFF' d='M23.922 11h4.026v17.298l-4.026 5.347h-4.026V16.323L23.922 11Z' />
        <path fill='#B2DDFF' d='m27.948 11-4.026 5.323v17.238l4.026-5.254V11Z' />
        <path fill='#53B1FD' d='M23.922 16.323h-4.026L23.922 11h4.026l-4.026 5.323Z' />
        <path fill='#84CAFF' d='M33.82 11h4.025v20.653L33.82 37h-4.026V16.323L33.82 11Z' />
        <path fill='#53B1FD' d='M33.82 16.323h-4.026L33.819 11h4.026l-4.026 5.323Z' />
        <path fill='#B2DDFF' d='M37.845 31.662V11l-4.026 5.322V37l4.026-5.338Z' />
      </g>
      <rect width={46} height={46} x={1} y={1} stroke='url(#d)' strokeWidth={2} rx={11} />
    </g>
    <defs>
      <linearGradient id='c' x1={24} x2={26} y1={0} y2={48} gradientUnits='userSpaceOnUse'>
        <stop stopColor='#fff' stopOpacity={0} />
        <stop offset={1} stopColor='#fff' stopOpacity={0.12} />
      </linearGradient>
      <linearGradient id='d' x1={24} x2={24} y1={0} y2={48} gradientUnits='userSpaceOnUse'>
        <stop stopColor='#fff' stopOpacity={0.12} />
        <stop offset={1} stopColor='#fff' stopOpacity={0} />
      </linearGradient>
      <clipPath id='b'>
        <rect width={48} height={48} fill='#fff' rx={12} />
      </clipPath>
      <filter id='a' width={48} height={51} x={0} y={-3} colorInterpolationFilters='sRGB' filterUnits='userSpaceOnUse'>
        <feFlood floodOpacity={0} result='BackgroundImageFix' />
        <feBlend in='SourceGraphic' in2='BackgroundImageFix' result='shape' />
        <feColorMatrix in='SourceAlpha' result='hardAlpha' values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0' />
        <feOffset dy={-3} />
        <feGaussianBlur stdDeviation={1.5} />
        <feComposite in2='hardAlpha' k2={-1} k3={1} operator='arithmetic' />
        <feColorMatrix values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0' />
        <feBlend in2='shape' result='effect1_innerShadow_6069_77629' />
      </filter>
    </defs>
  </svg>
)
