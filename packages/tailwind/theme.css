@theme {
  --*: initial;

  --spacing: 0.125rem;

  --breakpoint-xs: 26.25rem;
  --breakpoint-sm: 40rem;
  --breakpoint-md: 48rem;
  --breakpoint-lg: 64rem;
  --breakpoint-xl: 80rem;
  --breakpoint-2xl: 96rem;

  --blur-xs: 4px;
  --blur-sm: 8px;
  --blur-md: 12px;
  --blur-lg: 16px;
  --blur-xl: 24px;
  --blur-2xl: 40px;
  --blur-3xl: 64px;

  --aspect-video: 16 / 9;

  --font-base: "Inter", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
    "Segoe UI Emoji", Segoe UI Symbol, "Noto Color Emoji";
  --font-urbanist: "Urbanist", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
    "Segoe UI Emoji", Segoe UI Symbol, "Noto Color Emoji"; 

  --font-weight-thin: 100;
  --font-weight-extralight: 200;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;

  --radius-full: 100%;
  --radius-max: 2.097rem; /* 33554400px max css value */
  --radius-b1: 0.625rem; /* 10px */
  --radius-b2: 0.75rem; /* 12px */
  --radius-c1: 1rem; /* 16px */

  --text-2xs: .75rem;
  --text-2xs--line-height: calc(1 / 0.75);
  --text-xs: .875rem;
  --text-xs--line-height: calc(1.25 / 0.875);
  --text-base: 1rem;
  --text-base--line-height: calc(1.5 / 1);
  --text-sm: 1.125rem;
  --text-sm--line-height: calc(1.75 / 1.125);
  --text-md: 1.5rem;
  --text-md--line-height: 2rem;
  --text-lg: 1.875rem;
  --text-lg--line-height: 2.25rem;
  --text-xl: 2.25rem;
  --text-xl--line-height: 2.5rem;
  --text-2xl: 3rem;
  --text-2xl--line-height: 4rem;

  --color-white: oklch(1 0 0); /* #ffffff */
  --color-black: oklch(0 0 0); /* #000000 */
 
  --color-primary: oklch(0.6518 0.1809 253.92); /* #2e90fa */
  --color-error: oklch(0.6371 0.2104 28.54); /* #f04438 */
  --color-active: oklch(0.9744 0.0134 240.95); /* #eff8ff */
  --color-secondary: oklch(0.498 0.1953 291.5); /* #6941c6 */
  --color-secondary-light: oklch(0.9058 0.0559 306.14); /* #e9d7fe */
  --color-secondary-lighter: oklch(0.9761 0.013857 304.1415); /* #f9f5ff */
  --color-success: oklch(0.4987 0.1168 157.07); /* #067647 */
  --color-success-light: oklch(0.8958 0.0886 157.59); /* #abefc6 */
  --color-success-lighter: oklch(0.9788 0.0221 160.24); /* #ecfdf3 */

  --color-dim-1: oklch(0.3938 0.0197 266.05); /* #414651 */  
  --color-dim-2: oklch(0.4596 0.0174 264.39); /* #535862 */
  --color-dim-3: oklch(0.7281 0.0106 267.33); /* #A4A7AE */
  --color-title-1: oklch(0.2886 0.0236 264.1); /* #252b37 */
  --color-title-2: oklch(0.1469 0.0041 49.25); /* #0c0a09 */
  --color-whity: oklch(0.9674 0.0013 286.37); /* #f4f4f5 */

  --color-accessory-1: oklch(0.9276 0.0058 264.53); /* #e5e7eb */

  --color-turkey: oklch(0.6024 0.23 25.49); /* #eb212e */

  --color-transparent: transparent;

  --animate-fade-in: fade-in 0.2s ease-out;
  @keyframes fade-in {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }

  --animate-fade-in-scale-to-top: fade-in-scale-to-top 0.3s ease-out;
  @keyframes fade-in-scale-to-top {
    0% {
      opacity: 0;
      transform: scale(0.9) translate3d(0, 10%, 0);
    }
    100% {
      opacity: 1;
      transform: scale(1) translate3d(0, 0, 0);
    }
  }

  --animate-spin: spin 1s ease-in-out infinite;
  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);

  --shadow-layer-1: 0px 12px 16px -4px rgba(10, 13, 18, 0.08), 0px 4px 6px -2px rgba(10, 13, 18, 0.03), 0px 2px 2px -1px rgba(10, 13, 18, 0.04);
  --shadow-layer-2: 0px 0px 0px 1px rgba(10, 13, 18, 0.18) inset, 0px -2px 0px 0px rgba(10, 13, 18, 0.05) inset, 0px 1px 2px 0px rgba(10, 13, 18, 0.05),
}
