{"devDependencies": {"@ozaco/cli": "^0.0.13", "type-fest": "^4.41.0", "typescript": "^5.8.3", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "react-i18next": "^15.6.0", "react": "^19.1.0", "@legendapp/state": "^3.0.0-beta.31"}, "exports": {".": {"default": "./dist/index.js", "source": "./src/index.ts", "types": "./dist/index.d.ts"}}, "files": ["dist"], "name": "@mass/utils", "peerDependencies": {"typescript": ">= 5.8.3", "i18next": ">= 25.3.1", "i18next-browser-languagedetector": ">= 8.2.0", "i18next-http-backend": ">= 3.0.2", "react-i18next": ">= 15.6.0", "react": ">= 19.1.0", "@legendapp/state": ">= 3.0.0-beta.31"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "private": true, "type": "module", "version": "0.0.0"}