import { batch } from '@legendapp/state'
import { useObservable } from '@legendapp/state/react'
import { useRef } from 'react'

import { isEdge } from '../other/is'
import { throttle } from '../other/throttle'
import type { BlobType, Fn } from '../types/common'

export const useScroll = (
  throttleTime = 50,
  condition?: Fn<[node: HTMLElement, cb: () => void | undefined], BlobType>,
) => {
  const ref = useRef<HTMLElement>(null)
  const scroll = useObservable({
    x: 0,
    y: 0,

    xMax: 0,
    yMax: 0,

    xDirection: null as 'left' | 'right' | null,
    yDirection: null as 'up' | 'down' | null,
    dirty: false,
  })

  const update = () => {
    if (!ref.current) {
      return
    }

    const element = ref.current
    let maxY = element.scrollHeight - element.clientHeight
    const maxX = element.scrollWidth - element.clientWidth

    // Edge has a bug where scrollHeight is 1px bigger than clientHeight when there's no scroll.
    if (isEdge && maxY === 1 && element.scrollTop === 0) {
      maxY = 0
    }

    batch(() => {
      scroll.xMax.set(maxX)
      scroll.yMax.set(maxY)

      scroll.x.set(prevX => {
        if (prevX) {
          scroll.xDirection.set(element.scrollLeft - prevX > 0 ? 'right' : 'left')
        }

        return element.scrollLeft
      })
      scroll.y.set(prevY => {
        if (prevY) {
          scroll.yDirection.set(element.scrollTop - prevY > 0 ? 'down' : 'up')
        }

        return element.scrollTop
      })
      scroll.dirty.set(true)
    })
  }

  const throttledUpdate = throttle(update, throttleTime)

  const setRef = (node?: HTMLElement | null | undefined) => {
    if (node) {
      ref.current = node

      node.addEventListener('scroll', throttledUpdate)

      if (condition) {
        condition(node, throttledUpdate)

        return
      }

      throttledUpdate()
    } else {
      ref.current?.removeEventListener('scroll', throttledUpdate)
    }
  }

  return {
    ref,
    setRef,
    scroll,
  }
}
