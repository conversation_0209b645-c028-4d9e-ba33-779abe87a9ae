import type { BlobType } from '../types/common'

const fnv1aHash = (str: string): string => {
  let hash = 0x81_1c_9d_c5

  for (let i = 0; i < str.length; i++) {
    // biome-ignore lint/nursery/noBitwiseOperators: Redundant
    hash ^= str.charCodeAt(i)
    // biome-ignore lint/nursery/noBitwiseOperators: Redundant
    hash = (hash * 0x01_00_01_93) >>> 0
  }

  return hash.toString(16).padStart(8, '0')
}

const isJSONString = (str: string): boolean => {
  try {
    JSON.parse(str)
    return true
  } catch {
    return false
  }
}

const sortObjectKeys = (input: BlobType): BlobType => {
  if (typeof input === 'string') {
    if (isJSONString(input)) {
      const parsed = JSON.parse(input)
      const sorted = sortObjectKeys(parsed)
      return JSON.stringify(sorted)
    }
    return input
  }

  if (typeof input !== 'object' || input === null) {
    return input
  }

  if (Array.isArray(input)) {
    return input.map(sortObjectKeys).sort((a, b) => {
      const aStr = typeof a === 'object' ? JSON.stringify(a) : a
      const bStr = typeof b === 'object' ? JSON.stringify(b) : b
      return aStr < bStr ? -1 : aStr > bStr ? 1 : 0
    })
  }

  return Object.keys(input)
    .sort()
    .reduce((result: Record<string, BlobType>, key) => {
      result[key] = sortObjectKeys(input[key])
      return result
    }, {})
}

export const generateCacheKey = (obj: BlobType): string => {
  try {
    const sorted = sortObjectKeys(obj)
    const sortedStr = JSON.stringify(sorted)

    const encoder = new TextEncoder()
    const data = encoder.encode(sortedStr).toString()

    const hex = fnv1aHash(data)

    return hex
  } catch (err) {
    console.error('Failed to generate cache key:', err)
    throw err
  }
}
