import type { BlobType } from '../types/common'

export const throttle = <T extends (...args: BlobType[]) => BlobType>(
  func: T,
  wait: number,
): ((...args: Parameters<T>) => ReturnType<T> | undefined) => {
  let context: BlobType
  let args: Parameters<T>
  let result: ReturnType<T> | undefined
  let timeout: NodeJS.Timeout | null = null
  let previous = 0

  const later = (): void => {
    timeout = null
    result = func.apply(context, args)
    if (!timeout) {
      args = [] as BlobType
      context = null
    }
  }

  return function (this: BlobType, ...funcArgs: Parameters<T>): ReturnType<T> | undefined {
    const now = Date.now()
    const remaining = wait - (now - previous)
    context = this
    args = funcArgs

    if (remaining <= 0 || remaining > wait) {
      if (timeout) {
        clearTimeout(timeout)
        timeout = null
      }
      previous = now
      result = func.apply(context, args)
      if (!timeout) {
        args = [] as BlobType
        context = null
      }
    } else if (!timeout) {
      timeout = setTimeout(later, remaining)
    }
    return result
  }
}
